/**
 * @file bl0942_driver.h
 * @brief BL0942 Energy Monitoring IC Driver Header
 * @version 1.0
 * @date 2024
 * 
 * Driver for BL0942 calibration-free energy metering IC
 * Supports UART communication interface
 */

#ifndef BL0942_DRIVER_H
#define BL0942_DRIVER_H

#include "ql_api_osi.h"
#include "ql_api_uart.h"
#include "ql_log.h"

#ifdef __cplusplus
extern "C" {
#endif

/* BL0942 Register Addresses */
#define BL0942_REG_I_WAVE           0x01    // Current waveform data (20-bit signed)
#define BL0942_REG_V_WAVE           0x02    // Voltage waveform data (20-bit signed)
#define BL0942_REG_I_RMS            0x03    // Current RMS (24-bit unsigned)
#define BL0942_REG_V_RMS            0x04    // Voltage RMS (24-bit unsigned)
#define BL0942_REG_I_FAST_RMS       0x05    // Current fast RMS (24-bit unsigned)
#define BL0942_REG_WATT             0x06    // Active power (24-bit signed)
#define BL0942_REG_CF_CNT           0x07    // Active energy pulse counter (24-bit unsigned)
#define BL0942_REG_FREQ             0x08    // Line voltage frequency (16-bit)
#define BL0942_REG_STATUS           0x09    // System status (10-bit)

/* User Operation Registers */
#define BL0942_REG_I_RMSOS          0x12    // Current RMS offset (8-bit)
#define BL0942_REG_WA_CREEP         0x14    // Active power no-load threshold (8-bit)
#define BL0942_REG_I_FAST_RMS_TH    0x15    // Current fast RMS threshold (16-bit)
#define BL0942_REG_I_FAST_RMS_CYC   0x16    // Line cycle for current fast RMS (3-bit)
#define BL0942_REG_FREQ_CYC         0x17    // Line cycle for frequency measurement (2-bit)
#define BL0942_REG_OT_FUNX          0x18    // Logic output configuration (6-bit)
#define BL0942_REG_MODE             0x19    // User mode selection (10-bit)
#define BL0942_REG_GAIN_CR          0x1A    // Current channel gain (2-bit)
#define BL0942_REG_SOFT_RESET       0x1C    // Software reset (24-bit)
#define BL0942_REG_USR_WRPROT       0x1D    // User write protection (8-bit)

/* UART Communication Constants */
#define BL0942_UART_READ_CMD        0x58    // UART read command
#define BL0942_UART_WRITE_CMD       0xA8    // UART write command
#define BL0942_PACKET_READ_CMD      0xAA    // Packet read command
#define BL0942_PACKET_HEAD          0x55    // Packet header
#define BL0942_WRITE_PROTECT_KEY    0x55    // Write protection key
#define BL0942_SOFT_RESET_KEY       0x5A5A5A // Software reset key

/* UART Configuration */
#define BL0942_UART_PORT            QL_UART_PORT_2
#define BL0942_UART_BAUDRATE        QL_UART_BAUD_9600
#define BL0942_UART_DATA_BITS       QL_UART_DATABIT_8
#define BL0942_UART_STOP_BITS       QL_UART_STOPBIT_1
#define BL0942_UART_PARITY          QL_UART_PARITY_NONE
#define BL0942_UART_FLOW_CTRL       QL_UART_FC_NONE

/* Timing Constants */
#define BL0942_BYTE_DELAY_MS        20      // Maximum byte-to-byte delay
#define BL0942_FRAME_DELAY_MS       20      // Frame-to-frame delay
#define BL0942_READ_RESPONSE_US     150     // Response delay for read operations
#define BL0942_PACKET_READ_TIME_MS  50      // Time for packet read (23 bytes at 9600bps)

/* Data Structure Sizes */
#define BL0942_DATA_FRAME_SIZE      6       // Command + Address + 3 data bytes + checksum
#define BL0942_PACKET_SIZE          23      // Full packet read size
#define BL0942_MAX_RETRIES          3       // Maximum communication retries

/* BL0942 Energy Data Structure */
typedef struct {
    uint32_t i_rms;         // Current RMS (mA)
    uint32_t v_rms;         // Voltage RMS (mV)
    uint32_t i_fast_rms;    // Current fast RMS (mA)
    int32_t  watt;          // Active power (mW)
    uint32_t cf_cnt;        // Energy pulse counter
    uint16_t freq;          // Line frequency (Hz * 100)
    uint16_t status;        // System status
    uint32_t timestamp;     // Measurement timestamp
} bl0942_energy_data_t;

/* BL0942 Configuration Structure */
typedef struct {
    uint8_t  current_gain;      // Current channel gain (0-3)
    uint8_t  rms_update_sel;    // RMS update time selection (0=400ms, 1=800ms)
    uint8_t  ac_freq_sel;       // AC frequency selection (0=50Hz, 1=60Hz)
    uint8_t  fast_rms_sel;      // Fast RMS waveform selection
    uint8_t  cf_en;             // Energy pulse output enable
    uint8_t  wa_creep;          // Active power no-load threshold
    uint16_t i_fast_rms_th;     // Current fast RMS threshold
} bl0942_config_t;

/* Function Return Codes */
typedef enum {
    BL0942_OK = 0,
    BL0942_ERROR_INIT,
    BL0942_ERROR_COMM,
    BL0942_ERROR_CHECKSUM,
    BL0942_ERROR_TIMEOUT,
    BL0942_ERROR_PARAM,
    BL0942_ERROR_WRITE_PROTECT
} bl0942_result_t;

/* Function Prototypes */

/**
 * @brief Initialize BL0942 driver and UART interface
 * @return bl0942_result_t Result code
 */
bl0942_result_t bl0942_init(void);

/**
 * @brief Deinitialize BL0942 driver
 * @return bl0942_result_t Result code
 */
bl0942_result_t bl0942_deinit(void);

/**
 * @brief Configure BL0942 with specified parameters
 * @param config Pointer to configuration structure
 * @return bl0942_result_t Result code
 */
bl0942_result_t bl0942_configure(const bl0942_config_t *config);

/**
 * @brief Read single register from BL0942
 * @param reg_addr Register address
 * @param data Pointer to store read data (24-bit)
 * @return bl0942_result_t Result code
 */
bl0942_result_t bl0942_read_register(uint8_t reg_addr, uint32_t *data);

/**
 * @brief Write single register to BL0942
 * @param reg_addr Register address
 * @param data Data to write (24-bit)
 * @return bl0942_result_t Result code
 */
bl0942_result_t bl0942_write_register(uint8_t reg_addr, uint32_t data);

/**
 * @brief Read all energy parameters in packet mode
 * @param energy_data Pointer to energy data structure
 * @return bl0942_result_t Result code
 */
bl0942_result_t bl0942_read_energy_packet(bl0942_energy_data_t *energy_data);

/**
 * @brief Read individual energy measurements
 * @param energy_data Pointer to energy data structure
 * @return bl0942_result_t Result code
 */
bl0942_result_t bl0942_read_energy_data(bl0942_energy_data_t *energy_data);

/**
 * @brief Perform software reset of BL0942
 * @return bl0942_result_t Result code
 */
bl0942_result_t bl0942_soft_reset(void);

/**
 * @brief Calculate checksum for UART communication
 * @param data Pointer to data buffer
 * @param len Length of data
 * @param cmd Command byte
 * @return uint8_t Calculated checksum
 */
uint8_t bl0942_calculate_checksum(const uint8_t *data, uint8_t len, uint8_t cmd);

/**
 * @brief Convert raw register values to physical units
 * @param raw_data Pointer to raw energy data
 * @param energy_data Pointer to converted energy data
 */
void bl0942_convert_raw_data(const bl0942_energy_data_t *raw_data, bl0942_energy_data_t *energy_data);

#ifdef __cplusplus
}
#endif

#endif /* BL0942_DRIVER_H */
