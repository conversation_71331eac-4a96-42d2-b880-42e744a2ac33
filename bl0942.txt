                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  1 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
BL0942 datasheet 
BL0942 calibration-free Metering IC 
Datasheet 
 
 
 
 
 
 
 
 
 
 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  2 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
Table of Contents 
1 Product Description ..................................................................................................................................... 4 
1.1 Function Introduction ................................................................................................................... 4 
1.2 Features ........................................................................................................................................ 4 
1.3 System Block Diagram .................................................................................................................. 5 
1.4 Pin Configuration and Function Description................................................................................. 6 
1.5 Register List................................................................................................................................... 8 
1.6 Special Register Description ......................................................................................................... 9 
1.7 Specifications .............................................................................................................................. 11 
1.7.1 Electrical Parameters .......................................................................................................... 11 
1.7.2 Absolute Maximum Ratings ................................................................................................ 12 
2 Theory of Operation .................................................................................................................................. 13 
2.1 Current and Voltage Instantaneous Waveform ......................................................................... 13 
2.2 Active Power ............................................................................................................................... 13 
2.3 Anti-creep of Active Power ......................................................................................................... 14 
2.4 Energy Measurement ................................................................................................................. 15 
2.5 Voltage/Current RMS ................................................................................................................. 15 
2.6 Over current detection ............................................................................................................... 16 
2.7 Zero-crossing Detection .............................................................................................................. 18 
2.8 Line Voltage Frequency Detection.............................................................................................. 19 
3 Communication Interface .......................................................................................................................... 20 
3.1 SPI ............................................................................................................................................... 20 
3.1.1 Operation Mode .................................................................................................................. 20 
3.1.2 Frame Structure .................................................................................................................. 21 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  3 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
3.1.3 Fault Tolerant Mechanism of SPI Interface ......................................................................... 21 
3.2 UART ........................................................................................................................................... 22 
3.2.1 Baud Rate Configuration ..................................................................................................... 22 
3.2.2 Per Byte Format .................................................................................................................. 22 
3.2.3 Write Timing........................................................................................................................ 23 
3.2.4 Read Timing ......................................................................................................................... 23 
3.2.5 Timing Description .............................................................................................................. 24 
3.2.6 Packet Reading Mode ......................................................................................................... 25 
3.2.7 Protection Mechanism of UART Interface .......................................................................... 26 
4 Order Information ...................................................................................................................................... 26 
5 Marking information .................................................................................................................................. 26 
6 Package Dimensions .................................................................................................................................. 27 
 
 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  4 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
1 Product Description 
1.1 Function Introduction  
BL0942 is a built-in clock calibration-free energy metering IC, which is suitable for single-phase multi
function electricity meters, smart sockets, smart home appliances and other applications, with more cost
efficient solution. 
BL0942 incorporates two sigma delta ADC with a high accuracy energy measurement core. It measures 
line voltage and current and calculate active energy as well as instantaneous voltage and current. 
BL0942 provides access to on-chip registers via UART/SPI interfaces. One configurable low jitter pulse 
output Pin provide pulse that is proportional to active energy.  Zero-crossing voltage (ZX_V), zero-crossing 
current (ZX_I) and overcurrent are accessible via the external pin. 
BL0942 has a patent active power no-load detection to prevent meter-creep. 
1.2 Features  
⚫ Two independent Sigma-Delta ADC, one current and one voltage. 
⚫ Less than 0.1% error in active energy measurement over a dynamic range of 4000:1 
⚫ Current RMS range (10mA ~ 30A) @ 1mOhm 
⚫ Measure current, voltage RMS, fast current RMS, active power, sampled waveform 
⚫ The batch factory gain error is less than 1%, and the peripheral components can be free of calibration 
under certain conditions 
⚫ Programmable over-current detection 
⚫ ZX_V/ZX_I output 
⚫ Provide sampled waveform data for load type analysis 
⚫ SPI (maximum speed 900kHz)/ UART (4800~38400bps) communication(BL0942 TSSOP14L supports 
cascading up to 4 ICs through UART communication) 
⚫ On-chip power supply monitor 
⚫ On-chip reference 1.218v(typical) 
⚫ On-chip oscillator as clock source 
⚫ Single 3.3V supply, low power consumption 10mW (typical) 
⚫ SSOP10L/TSSOP14L package 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  5 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
1.3 System Block Diagram  
IP
 ZX
 CF1
 SCLK
 LDO
 Reference 
Voltage 
Power 
On/Reset
 Internal 
Clock 
UART
 /SPI
 BL0942
 VP
 1.218V
 SEL
 RX/SDI
 TX/SDO
 GND
 VDD
 DSP
 SDM PGA
 SDM PGA REG
 CF2
 IN
 VN
 
          Figure 1 
  
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  6 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
1.4 Pin Configuration and Function Description 
   
             Figure 2  
Pin Description (SSOP10L) 
Pin No Mnemonic Description 
1 VDD Power supply ( 3.3V) 
2,3 IP, IN Analog input for Current Channel, this differential voltage input has a maximum 
input range of ± 42mV p-p (30mV RMS) 
4 VP Analog input for Voltage Channel, this voltage input has a maximum input range 
of ± 100mV p-p (70mV RMS) 
5 GND Ground reference 
6 CF1 Logic output. See the OT_FunX register configuration section 
7 SEL UART/SPI mode selection (0: UART 1: SPI), internal pull-down resistance, 
connected to GND is 0 level (UART), connected directly to VDD is high level (SPI) 
8 SCLK_BPS Serial Clock input for SPI. If using UART, this pin is used to config baud rate of 
UART 
9 RX/SDI Receive Line for UART interface/Data input for SPI interface, need external pull
up resistor for UART interface. 
10 TX/SDO Transmit Lint for UART interface/Data output for SPI interface, need external 
pull-up resistor for UART interface. 
  
1
 2
 3
 4
 5
 10
 9
 8
 7
 6
 RX/SDI
 TX/SDO VDD
 IP
 IN
 VP
 GND CF1
 SCLK_BPS
 SEL
 BL0942
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  7 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
   
     Figure 3 
Pin Description (TSSOP14L) 
Pin No Mnemonic Description 
1 VDD Power supply ( 3.3V) 
2,3 IP,IN Analog input for Current Channel, this differential voltage input has a 
maximum input range of ± 42mV p-p (30mV RMS) 
4 VP Analog input for Voltage Channel, this voltage input has a maximum input 
range of ± 100mV p-p (70mV RMS) 
5 GND Ground reference 
6 A1 Chip Address set for UART interface 
7 A2_NCS Chip select for SPI interface. 
Chip Address set for UART interface 
8 CF2 Logic output. See the OT_FunX register configuration section 
9 ZX Zero crossing Voltage logic output. See the OT_FunX register configuration 
section 
10 CF1 Logic output. See the OT_FunX register configuration section 
11 SEL UART/SPI mode selection (0: UART 1: SPI), internal pull-down resistance, 
connected to GND is 0 level (UART), connected directly to VDD is high level 
(SPI) 
12 SCLK_BPS Serial Clock input for SPI. If using UART, this pin is used to config baud rate of 
UART 
13 RX/SDI Receive Line for UART interface/Data input for SPI interface, need external 
pull-up resistor for UART interface. 
14 TX/SDO Transmit Line for UART interface/Data output for SPI interface, need external 
pull-up resistor for UART interface. 
  
1 VDD
 BL0942
 2
 3
 4
 5
 6
 7
 14
 13
 12
 11
 10
 9
 8
 IN
 VP
 GND
 IP
 A1
 RX/SDI
 SCLK_BPS
 SEL
 CF1
 ZX
 A2_NCS
 TX/SDO
 CF2
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  8 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
1.5 Register List 
Address Register Name Significant bit Default  Register Description  
Read-only register  
0x01 I_WAVE 20 0x00000 Current waveform data, signed  
0x02 V_WAVE 20 0x00000 Voltage waveform data, signed  
0x03 I_RMS 24 0x000000 Current RMS, unsigned  
0x04 V_RMS 24 0x000000 Voltage RMS, unsigned  
0x05 I_FAST_RMS 24 0x000000 Current fast RMS, unsigned  
0x06 WATT 24 0x000000 Active power, signed  
0x07 CF_CNT 24 0x000000 Active energy pulse counter, unsigned  
0x08 FREQ 16 0x4E20 Line voltage frequency  
0x09 STATUS 10 0x000 System Status  
User operation register (read / write)  
0x12 I_RMSOS 8 0x00 Current RMS Offset  
0x14 WA_CREEP 8 0x0B Active power No-load threshold  
0x15 I_FAST_RMS_TH 16 0xFFFF Current fast RMS threshold  
0x16 I_FAST_RMS_CYC 3 0x1 Line cycle for Current fast RMS 
measurement 
 
0x17 FREQ_CYC 2 0x3 Line cycle for Line voltage frequency 
measurement 
 
0x18 OT_ FUNX 6 0x24 Logic output configuration  
0x19 MODE 10 0x87 User mode selection  
0x1A GAIN_CR 2 0x2 Current channel gain  
0x1C SOFT_RESET 24 0x000000 Software reset, BL0942 resets if 0x5a5a5a is 
written to this register 
 
0x1D USR_WRPROT 8 0x00 
User write protection. Only 0x55 is written 
to this register, the user operation register 
can be written 
 
 
Note: the data frame of communication protocol is 24bit, and the upper invalid bit need be supplemented 
with 0. 
  
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  9 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
1.6 Special Register Description 
User mode selection register 
 
0x19 MODE  
Bits Bit Name  Default Description 
[1:0] reserved b11 reserved 
[2] CF_EN b1 Active energy and pulse 
output Enable 
0: Disable 
1: Enable 
[3] RMS_UPDATE_SEL b0 Selection of refresh time 
for RMS 
0：400ms 
1：800ms 
[4] FAST_RMS_SEL b0 FAST_RMS waveform from； 
0：full wave；1；AC wave 
[5] AC_FREQ_SEL b0 Selection of AC frequency 0：50Hz 
1：60Hz 
[6] CF_CNT_CLR_SEL b0 Clear after read of CF_CNT 
register Enable 
0: Disable 
1: Enable 
[7] CF_CNT_ADD_SEL b1 
Mode selection of active 
energy pulse 
accumulation 
0: Signed accumulation mode 
1: Absolute accumulation mode 
[9:8] UART_RATE_SEL b00 Baud rate selection 
00 
01  
The baud rate is decided 
by the external pin 
SCLK_BPS 
SCLK_BPS=0, 4800bps 
SCLK_BPS=1, 9600bps 
10 19200bps 
11 38400bps 
[23:10] reserved b0 reserved 
 
  
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  10 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
Logic Output configuration register 
 
0x18 OT_ FUNX  
Bits Bit Name  Default Description 
[1:0] CF1_FUNX_SEL b00 
CF1 output selection bit: 
b00: Active energy calibration pulse Output (CF)  
b01: Logic output of over-current event(O_C) 
b10: Logic output of zero crossing voltage (ZX_V) 
b11: Logic output of zero crossing current (ZX_I) 
[3:2] CF2_FUNX_SEL b01 
CF2 output selection bit: 
b00: Active energy calibration pulse Output (CF)  
b01: Logic output of over-current event(O_C) 
b10: Logic output of zero crossing voltage (ZX_V) 
b11: Logic output of zero crossing current (ZX_I) 
[5:4] ZX_FUNX_SEL b10 
ZX output selection bit: 
b00: Active energy calibration pulse Output (CF)  
b01: Logic output of over-current event(O_C) 
b10: Logic output of zero crossing voltage (ZX_V) 
b11: Logic output of zero crossing current (ZX_I) 
[23:6] reserved b0 reserved 
 
System Status register 
0x09 STATUS  
Bits Bit Name  Default Description 
[0] CF_REVP_F b0 This bit indicates the direction of the last energy Pulse CF 
0: active forward;   1: active reverse  
[1] CREEP_F b0 This bit indicates whether the BL0942 is in active power no-load status 
0: not active power no-load state;  1: active power no-load state 
[7:2] reserved b0 reserved 
[8] I_ZX_LTH_F b0 This bit indicates the current signal is below zero crossing current 
detection threshold 
[9] V_ZX_LTH_F b0 This bit indicates the current signal is below zero crossing current 
detection threshold 
[23:10] reserved b0 reserved 
 
The Gain config of Current channel 
0x1A GAIN_CR The Gain config of Current channel 
No. name  default value description 
[1:0] GAIN_CR B10 00：Gain=1；        01：Gain=4； 
10：Gain=16(default)；11：Gain=24； 
[23:10] reserved b0 reserved 
  
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  11 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
1.7 Specifications 
1.7.1 Electrical Parameters 
(VDD = 3.3V, GND = 0V, 25 ℃, Energy accuracy error measure via CF output) 
Parameter Symbol Test conditions Min Typ Max Unit 
Power Supply VDD  3.0  3.6 V 
Power Consumption Iop VDD=3.3V  3  mA 
Active Energy 
Measurement Error  Dynamic range 4000:1  0.1  % 
Active Power 
Measurement Error 
(big signal) 
 30A~100mA@1mOhm 
sampling resistor  0.2  % 
Active Power 
Measurement Error 
(small signal) 
 100mA~50mA@1mOhm 
sampling resistor  0.4  % 
Active Power 
Measurement Error 
(small signal) 
 50mA~10mA@1mOhm 
sampling resistor  0.6  % 
RMS Measurement 
Error 
(big signal) 
 35A~100mA@1mOhm 
sampling resistor  0.2  % 
RMS Measurement 
Error 
(small signal) 
 100mA~50mA@1mOhm 
sampling resistor  2  % 
RMS Measurement 
Error 
(small signal) 
 50mA~10mA@1mOhm 
sampling resistor  6  % 
Refresh time of 
I_FAST_RMS 
50Hz Set by I_FAST_RMS_CYC 
register 
10  160 mS 
60Hz 8.3  133 mS 
Zero-crossing logic 
output delay  ZX_V/ZX_I  570  uS 
Phase Error 
between Channels 
PF08err Phase lead 37° 
（PF=0.8C）   0.5 % 
PF05err Phase lag 60° 
（PF=0.5L）   0.5 % 
AC Power Supply  
Rejection (output 
frequency variation) 
ACPSRR IP/N=100mV  0.1  % 
DC Power Supply  DCPSRR VP/N=100mV  0.1  % 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  12 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
Rejection (output 
frequency variation) 
Analog input 
(current)  differential input (peak)   42 mV 
Analog input 
(voltage)  differential input (peak)   100 mV 
Analog input 
impedance  IP, IN,VP  370  kΩ 
SEL pull-down 
resistor  SEL PIN  56.9  kΩ 
Analog input 
bandwidth  （-3dB）  3.5  kHz 
On-chip reference  Vref   1.218  V 
Logic input high 
voltage VINH VDD=3.3V±5% 2.6   V 
Logic input low 
voltage VINL VDD=3.3V±5%   0.8 V 
Logic output high 
voltage VOH VDD=3.3V±5% 
IOH=5mA VDD-0.5   V 
Logic output low 
voltage VOL VDD=3.3V±5% 
IOL=5mA   0.5 V 
 
1.7.2 Absolute Maximum Ratings 
（T = 25 ℃） 
Paramter Symbol Rating Unit 
Supply Voltage VDD VDD -0.3 ~ +4 V 
Analog Input Voltage (to GND) IP, IN, VP -4 ~ +4 V 
Digital Input Voltage (to GND) A1, A2_NCS, SEL, SCLK_BPS, RX/SDI -0.3 ~ VDD+0.3 V 
Digital Output Voltage (to GND) CF1,CF2,ZX,TX/SDO -0.3 ~ VDD+0.3 V 
Operating Temperature Range Topr -40 ~ +85 ℃ 
Storage Temperature Range Tstr -55 ~ +150 ℃ 
 
  
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  13 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
2 Theory of Operation 
BL0942 includes two-channel PGA, two-channel sigma delta ADC, internal clock, POR, LDO and digital 
signal processing module (DSP). 
2.1 Current and Voltage Instantaneous Waveform 
 
  Figure 4 
The current and voltage waveform data are updated at a rate of 7.8kSPS (samples per second). Each 
sampling data is 20bit signed and stored in the waveform data register (I_WAVE, V_WAVE).  
Note: the register is 24bit. If the number of bits is insufficient, zero is added to the high invalid bit. 
address Register Name Significant bit Default  Register Description 
0x01 I_WAVE 20 0x00000 Current Waveform Data, signed 
0x02 V_WAVE 20 0x00000 Voltage Waveform Data, signed 
 
2.2 Active Power 
 
          Figure 5 
PGA ADC SINC3 HPF V_WAVE VP
 PGA ADC SINC3 HPF I_WAVE IP
 IN
 WA_CREEP
 I_WAVE
 V_WAVE
 WATT
 WATT_t
 × LPF_
 WATT
 WATT
 ANTI-CREEP AVERAGE
 RMS_UPDATE_SEL
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  14 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
address Register Name Significant bit Default  Register Description 
0x06 WATT 24 0x000000 Active power, signed 
WATT Register value: WATT= 3537∗𝐼(𝐴)∗𝑉(𝑉)∗COS（φ）
 
 𝑟𝑒𝑓2 
Where: 
I(A)/ V(V) is the rms voltage (mV) of the input signal of current/voltage channel. 
Φ is the angle between current and voltage signals. 
Vref is the on-chip reference voltage, the typical value is 1.218V; 
This register indicates whether the active power is positive or negative, bit [23] = 0, active power is positive 
power, bit [23] = 1, active power is negative power. complement code form. 
2.3 Anti-creep of Active Power 
Bl0942 has a patent active power no-load detection to prevent meter-creep, which ensures that the 
board level noise power will not accumulate energy when there is no-load. 
Active power anti-creep threshold register (WA_CREEP) is an 8-bit unsigned value, and the default value 
is 0x0B。When the absolute value of instantaneous active power is less than this threshold, the output 
active power is set to 0. This can make the value of the active power register is 0 even if there is a small 
noise signal in no-load, and the active energy is not accumulated. 
address Register Name Significant bit Default  Register Description 
0x14 WA_CREEP 8 0x0B Active power No-load threshold 
The corresponding relationship between the WA_CREEP and WATT register is shown in the following 
equation: 
WA_CREEP=WATT∗ 256
 3125 
  Note: when the BL0942 is in the Anti-creep state, the current RMS value is also cut off to 0. 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  15 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
2.4 Energy Measurement 
BL0942 provides energy pulse output. The active energy can be obtained by integrating the 
instantaneous active power for a period of time, and output the calibration pulse (CF). The CF_CNT register 
stores the number of output energy pulses CF, as shown in the figure below. 
              
           Figure 6 
Address Register Name Significant bit Default  Register Description 
0x07 CF_CNT 24 0x000000 Active energy pulse counter 
BL0942 provide active energy via CF_CNT register. The power information also can be provided through 
CF1/CF2/ZX pin.  When the active energy pulse period is shorter than 160mS, the duty cycle of the 
pulse output is 50%; when the pulse period is longer than 160mS, the high-level fixed pulse width is 
80mS. 
0x19 MODE Operating mode register 
No. name  default  description 
[2] CF_EN b1 Active energy and pulse 
output Enable 
0: Disable 
1: Enable 
[6] CF_CNT_CLR_SEL b0 Clear after read of CF_CNT 
register Enable 
0: Disable 
1: Enable 
[7] CF_CNT_ADD_SEL b1 Mode selection of active 
energy pulse accumulation 
0: Signed accumulation mode 
1: Absolute accumulation mode 
Cumulative time of each CF pulse tCF=
 1638.4∗256
 𝑊𝐴𝑇𝑇
  
Where: WATT is the value of active power register(0x06). 
2.5 Voltage/Current RMS 
The RMS algorithm of current and voltage is shown in the figure below. 
WATT_t CF_CNT ∫
 WA_CFDIV
 GEN
 _CF
 CF
 CNT_ADD_SEL
 CF_EN
 COUNT
 CF_REVP
 MUX
 CF_CNT_CLR
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  16 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
 
        Figure 7 
Address Register Name Significant bit Default  Register Description 
0x03 I_RMS 24 0x000000 Current RMS, unsigned 
0x04 V_RMS 24 0x000000 Voltage RMS, unsigned 
 
0x19 MODE Operating mode register 
No. name  default 
value description 
[3] RMS_UPDATE_SEL 0b0 Selection of refresh 
time for RMS 
0：400ms 
1：800ms 
Current RMS register equation: I_RMS = 305978∗𝐼(𝐴)
 𝑉𝑟𝑒𝑓 
Voltage RMS register equation: V_RMS = 73989∗𝑉(𝑉)
 𝑉𝑟𝑒𝑓 
Where: 
Vref : on-chip reference voltage, the typical value is 1.218 V. (the gain config of current is 16) 
I(A): the voltage of IP&IN Pin(unit: mV). 
V(V): the voltage of VP&GND Pin(unit: mV). 
2.6 Over current detection 
BL0942 provide over-current detection. The absolute value of I_WAVE_F is accumulated in half cycle or 
cycle time and stored in I_FAST_RMS register.  If I_FAST_RMS[23:8] >= I_FAST_RMS_TH[15:0], the over
current logic output is high level. 
x² LPF_RMS root I_RMS
 I_RMS_t
 AVERAGE I_WAVE
 x² LPF_RMS root V_RMS
 V_RMS_t
 AVERAGE
 RMS_UPDATE_SEL
 V_WAVE
 RMS_UPDATE_SEL
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  17 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
∫
 FAST_RMS
 || I_WAVE_F
 AC_FREQ_SEL
 >=
 [23:8] flag_l
 FAST_RMS_CTRL
 [15:0] 
          Figure 8 
Address Register Name Significant bit Default  Register Description 
0x15 I_FAST_RMS_TH 16 0xFFFF Current fast RMS threshold 
 
 
 _𝐹𝐴𝑆𝑇_𝑅𝑀𝑆≈𝐼_𝑅𝑀𝑆∗0.363 
NOTE: the FAST_RMS is only used for large signal measurement. The accuracy error of FAST_RMS will be 
large in small signal because of DC bias on the board.  
Address Register Name Significant bit Default  Register Description 
0x16 I_FAST_RMS_CYC 3 0x1 
Line cycle for Current fast RMS measurement 
000 0.5 cycles 
001 1 cycle 
010 2 cycles 
011 4 cycles 
other 8 cycles 
The I_FAST_RMS measure period can be config by I_FAST_RMS_CYC register. MODE [5] is used to config 
AC frequency. If MODE [5]=0, the AC frequency is 50Hz, then 1 cycle is 20mS; if MODE[5]=1, the AC 
frequency is 60Hz, 1cycle is 16.7mS. the shorter the measure period, the more the measure value jitter. If 
need remove the DC bias , set MODE[4]=1 to select the waveform after HPF as the FAST_RMS source  
0x19 MODE Operating mode register 
No. name  default value description 
[4] FAST_RMS_SET 0b0 FAST_RMS waveform from； 
0：full wave；1；AC wave 
[5] AC_FREQ_SEL 0b0 0：50Hz 
Address Register Name Significant bit Default  Register Description 
0x05 I_FAST_RMS 24 0x000000 Current fast RMS, unsigned 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  18 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
AC frequency 
selection 1：60Hz 
 
2.7 Zero-crossing Detection 
Bl0942 includes a zero-crossing detection on voltage and current channel.  The voltage and current 
channel ZX information is configured to be output on CF1/CF2/ZX Pin.  As shown in Figure 11, the ZX output 
goes high on the negative-going edge of the signal zero crossing and low on the positive-going edge of signal 
zero crossing. A delay of approximately 570uS should be expected on ZX logic output pin due to the time 
delay of HPF. 
Figure 11 
0x18 OT_ FUNX Output configuration register 
No. name  default value description 
[1:0] CF1_FUNX_SEL 0b00 
CF1 output selection bit: 
b00: Active energy calibration pulse Output (CF)  
b01: Logic output of over-current event(O_C) 
b10: Logic output of zero crossing voltage (ZX_V) 
b11: Logic output of zero crossing current (ZX_I) 
[3:2] CF2_FUNX_SEL 0b01 
CF2 output selection bit: 
b00: Active energy calibration pulse Output (CF)  
b01: Logic output of over-current event(O_C) 
b10: Logic output of zero crossing voltage (ZX_V) 
b11: Logic output of zero crossing current (ZX_I) 
[5:4] ZX_FUNX_SEL 0b10 
ZX output selection bit: 
b00: Active energy calibration pulse Output (CF)  
b01: Logic output of over-current event(O_C) 
b10: Logic output of zero crossing voltage (ZX_V) 
b11: Logic output of zero crossing current (ZX_I) 
570us
 ZX
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  19 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
[23:6] reserved 0b0 reserved 
 
 
0x19 STATUS Working status register 
No. name  default  description 
[8] I_ZX_LTH_F 0b0 
This bit indicates the current 
signal is below zero crossing 
current detection threshold 
0: Current zero crossing detection is valid 
1: Current zero crossing detection is invalid 
[9] V_ZX_LTH_F 0b0 
This bit indicates the current 
signal is below zero crossing 
current detection threshold 
0: Voltage zero crossing detection is valid 
1: Voltage zero crossing detection is invalid 
 
 To prevent spurious zero crossing when a small input is present, there have zero-crossing threshold on 
all channels of the BL0942.  
 The fixed threshold of current channel is approximately set to a range of 64:1 of the input full scale. The 
fixed threshold of voltage channel is approximately set to a range of 32:1 of the input full scale. If any input 
signal falls below these levels, no zero-crossing signals are produced by BL0942, the ZX logic output keep low 
level. 
2.8 Line Voltage Frequency Detection 
BL0942 provides a frequency measurement of the voltage channel. The FREQ register is updated once 
every set FREQ_CYC cycle.   
Address Register Name Significant bit Default  Register Description 
0x08 FREQ 16 0x4e20 Line voltage register, unsigned 
0x17 FREQ_CYC 2 0x3 
Line voltage refresh time setting register 
00 2 cycles 
01 4 cycles 
10 8 cycles 
11 16 cycles 
 
The frequency measurement has a resolution of 2us/LSB, which represents 0.01% when the line 
frequency is 50Hz and 0.012% when the line frequency is 60Hz. 
 The following equation can be used to compute the line frequency using the FREQ register: 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  20 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
 
 measure=1000000
 𝐹𝑅𝐸𝑄
 Hz 
In addition, when the RMS voltage is lower than the zero-crossing threshold, the line voltage frequency 
detection is disable. 
3 Communication Interface 
BL0942 provides SPI/UART communication interfaces, these communication interface use the same 
group of pins. So only one method of communication can be used in each design. The length of the data 
transfer is 24bits. If the width of the register is less than 24bits long, the high invalid bit should be fixed with 
0, make up 24bits long to transfer. 
3.1 SPI 
⚫ If SEL Pin is pulled up to VDD, the communication method is SPI 
⚫ slave mode 
⚫ Half duplex communication, the maximum SPI speed is 900khz 
⚫ 8-bits data transmission, data order with MSB-first shifting 
⚫ Fixed clock polarity and phase (CPOL = 0, CPHA = 1) 
⚫ three or four wire communication method. In three wire mode, A2_NCS is connected to GND. In four
wire mode, the A2_NCS must be driven low for the entire read or write operation. 
3.1.1 Operation Mode 
CPOL = 0 and CPHA = 1.  SCLK PIN has a low-level idle state. The second edge on the SCLK Pin is the 
MSBIT capture strobe. Data is latched on the occurrence of the first clock transition.  
 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  21 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
          Figure 12 
3.1.2 Frame Structure 
In SPI communication method, first send 8bit initial byte (0x58) or (0xa8), (0x58) is read operation 
identification byte, (0xa8) is write operation identification byte, and then send register address of BL0942. 
The following figure shows the data transfer sequence of SPI Write and Read operations.  A complete 
read/write operation contain 48 cycles. 
1） Write operation frame 
1 0 1 0 1 0 0 0 A
 7
 A
 6
 A
 5
 A
 4
 A
 3
 A
 2
 A
 1
 A
 0
 D
 7
 D
 6
 D
 5
 D
 4
 D
 3
 D
 2
 D
 1
 D
 0
 Register Address Write CMD Data_H
 D
 7
 D
 6
 D
 5
 D
 4
 D
 3
 D
 2
 D
 1
 D
 0
 Data_L
 D
 7
 D
 6
 D
 5
 D
 4
 D
 3
 D
 2
 D
 1
 D
 0
 Checksum
 
where the checksum byte = ((0xa8 + Address + Data_H + Data_M+ Data_L) & 0xff) and then inverted 
by bit. 
2） Read operation frame 
0 1 0 1 1 0 0 0 A
 7
 A
 6
 A
 5
 A
 4
 A
 3
 A
 2
 A
 1
 A
 0
 Register Address Read CMD
 D
 7
 D
 6
 D
 5
 D
 4
 D
 3
 D
 2
 D
 1
 D
 0
 Data_H
 D
 7
 D
 6
 D
 5
 D
 4
 D
 3
 D
 2
 D
 1
 D
 0
 Data_L
 D
 7
 D
 6
 D
 5
 D
 4
 D
 3
 D
 2
 D
 1
 D
 0
 Checksum
 
where the checksum byte is ((0x58 + Address + Data_H + Data_M+ Data_L) & 0xff) and then inverted 
by bit. 
Note: the register data is fixed to 3 bytes (MSB first), if the effective byte of the register data is less than 3 
bytes, the invalid bit should be filled with 0) 
 
3.1.3 Fault Tolerant Mechanism of SPI Interface 
If MCU send 6 bytes (0xFF), the BL0942 perform a reset function on the SPI communication interface. 
  
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  22 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
3.2 UART 
⚫ SEL pin connected to GND, SEL = 0, UART communication method. 
⚫ slave mode 
⚫ Half duplex communication, the baud rate can be configured to 4800bps,9600bps, 
19200bps,38400bps. 
⚫ 8-bit data transmission, no parity bit, stop bit 1 
⚫ Support packet reading 
⚫ TSSOP14L package can support the chip address function of the device, and the IC Address can be 
configurated to 0~3 by pins [A2_NCS, A1]. These IC Address set allow communication with multiple 
devices (maximum 4 pcs of BL0942) on the one UART interface of MCU. 
 
3.2.1 Baud Rate Configuration 
BL0942 can configurate Baud Rate by register MODE [9:8] and SCLK_BPS pin. 
0x19 MODE Operating mode register 
No. name  default value description 
[9:8] UART_RATE_SEL 0b00 Baud rate selection 
00  SCLK_BPS pin = 0: 4800bps 
SCLK_BPS pin = 1: 9600bps 
01 Same as 00 
10 19200bps 
11 38400bps 
 
3.2.2 Per Byte Format 
t1 t2 t3
 Start Bit D0 D1 D2 D3 D4 D5 D6 D7 Stop Bit Byte 
Take baud rate = 4800bps as an example 
Start bit low level duration T1 = 208us 
Effective data bit duration T2 = 208 * 8 = 1664us 
Stop bit high level duration T3 = 208us 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  23 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
3.2.3 Write Timing 
The UART data writing sequence of the host is shown in the figure below. The host first sends 
command bytes {1,0,1,0,1,0, A2, A1}, and then sends the register address that need to write data. Next, the 
three data bytes are sent (the low byte is first, the high byte is later, if the valid bytes of the data is less than 
3 bytes, the invalid byte is supplemented with 0), and finally the CHECKSUM byte is sent. 
 
Where: 
{1,0,1,0,1,0, A2, A1} is the frame initial byte of the write operation. If PIN A2_NCS=1, A1=0, the BL0942 
address is 2, the frame initial byte is 0XAA. 
ADDR is the register address of BL0942 that need to write data. 
The CHECKSUM byte is ({1,0,1,0,1,0, A2, A1} + ADDR + DATA [7:0] + DATA [15:8] + DATA [23:16]) & 0xff then 
inverted by bit. 
3.2.4 Read Timing 
The UART data reading sequence of the host is shown in the figure below. The host first sends 
command byte {0,1,0,1,1,0, A2, A1}, and then sends the register address to be read. Next, BL0942 will return 
three data bytes (low byte comes first, high byte comes last, invalid bytes supplemented with 0 if valid byte 
is less than 3 bytes), and finally CHECKSUM byte. 
 
Where: 
{1,0,1,0,1,0,
 A2,A1} ADDR[0:7] DATA[0:7] DATA[16:23] CHECKSUM[0:7] RX ……
 t0
 t1 t1 t1
 (0,1,0,1,1,0,
 A2,A1) ADDR[0:7]
 DATA[0:7] DATA[16:23] CHECKSUM[0:7]
 RX
 TX ……
帧头……
 t0
 t1
 t2
 t0
 t4 t3
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  24 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
{0,1,0,1,1,0, A2, A1} is the frame initial byte of read operation, assuming {A2, A1} = 10, the BL0942 
address is 2, the frame initial byte is 0x5A. 
ADDR is the register address of BL0942 need to read data. 
The CHECKSUM byte is ({0,1,0,1,1,0, A2, A1} + ADDR + DATA [7:0] + DATA [15:8] + DATA [23:16]) & 0xff then 
inverted by bit. 
Note: the IC Address of SSOP10L package is 0. 
3.2.5 Timing Description 
 Description Min Type Max Unit 
t1 Byte-to Byte Delay by master 0  20 mS 
t2 Delay between the end of MCU sending register address and 
BL0942 sending byte in read operation 
 150  uS 
t3 Frame-to-Frame delay 0.5   uS 
t4 Byte-to Byte Delay by BL0942  0  uS 
 
  
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  25 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
3.2.6 Packet Reading Mode 
The MCU sends a packet of two bytes "{0,1,0,1,1,0, A2, A1} + 0xAA", BL0942 will return a full electric 
parameter data packet. A total of 23 bytes are returned, which takes about 48ms when 4800bps is used. 
All electric parameter package format: 
 Send byte order content 
HEAD 0 0x55 
I_RMS 
1 I_RMS [7:0] 
2 I_RMS [15:8] 
3 I_RMS [23:16] 
V_RMS 
4 V_RMS [7:0] 
5 V_RMS [15:8] 
6 V_RMS [23:16] 
I_FAST_RMS 
7 I_FAST_RMS [7:0] 
8 I_FAST_RMS [15:8] 
9 I_FAST_RMS [23:16] 
WATT 
10 WATT [7:0] 
11 WATT [15:8] 
12 WATT [23:16] 
CF_CNT 
13 CF_CNT [7:0] 
14 CF_CNT [15:8] 
15 CF_CNT [23:16] 
FREQ 
16 FREQ [7:0] 
17 FREQ [15:8] 
18 0x00 
STATUS 
19 STATUS [7:0] 
20 0x00 
21 0x00 
CHECKSUM 22  
 
checksum=(({0,1,0,1,1,0,A2,A1} + 0x55 + data1_l + data1_m + data1_h +……) & 0xff) then inverted by bit. 
 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  26 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
3.2.7 Protection Mechanism of UART Interface 
⚫ If Byte-to Byte Delay by master exceeds 20ms, UART interface will reset. 
⚫ Command reset, the UART interface of BL0942 continuously receives more than 32 "0", and the UART 
interface is reset. 
 
Note: in UART mode, multi-chip communication, Frame-to-Frame delay when switching to 
communicate with another IC should keep above 20mS.  
 
4 Order Information 
BL0942-X X=SSOP10L: SSOP10L package 
  X=TSSOP14L: TSSOP14L package 
5 Marking information 
TSSOP14L              SSOP10L 
“YY” represents the packaging year 
“WW” represents packaging week, 01-52 week 
“ZZ” represents packaging plant 
 “#”represents space 
"SSSSS" represents LOT Number 
 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  27 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
6 Package Dimensions 
Humidity sensitivity grade: MSL3 
The warranty period: two years 
Packaging mode: SSOP10L tape packaging 
Minimum package:3000 
 
 
 
                                              
BL0942      calibration-free metering IC
 
Shanghai Belling Corp., Ltd.                            V1.06                                                  28 / 28 
810, Yishan Road, Shanghai, China, 200233 Tel: +86-21-24261000 
www.belling.com.cn 
Humidity sensitivity grade: MSL3 
The warranty period: two years 
Packaging method: TSSOP14L tape packaging 
Minimum package: 3000 
 
 
 
 
 
 