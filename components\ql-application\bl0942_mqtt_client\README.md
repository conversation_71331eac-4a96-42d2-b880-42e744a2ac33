# BL0942 MQTT Energy Monitor for EC200U-EU

A complete embedded C solution for energy monitoring using the BL0942 energy measurement IC connected to an EC200U-EU cellular module. The system automatically connects to the best available cellular network (LTE/3G/EDGE), communicates with the BL0942 via UART, and transmits energy data to an MQTT broker over the internet.

## Features

### Cellular Connectivity
- **Automatic Network Selection**: Connects to the best available network (LTE → 3G → EDGE)
- **APN Configuration**: Uses "atmirancell" APN with user-provided credentials
- **Network Monitoring**: Real-time network status and signal strength monitoring
- **Auto-Reconnection**: Automatic reconnection on network failures

### MQTT Communication
- **MQTT Broker**: Connects to **************:1883
- **Authentication**: Username/Password: admin/admin
- **Client ID**: 12345678
- **Topic**: EnergyMeter1
- **JSON Payload**: Structured energy data transmission
- **QoS Level 1**: Ensures message delivery

### BL0942 Energy Monitoring
- **UART Communication**: 4800 baud, 8N1 configuration
- **Packet Mode**: Efficient bulk data reading
- **Real-time Sampling**: Configurable sampling intervals (default: 5 seconds)
- **Data Conversion**: Raw values converted to physical units
- **Error Handling**: Robust communication error detection and recovery

### Debug Interface
- **Debug UART**: 115200 baud on UART1 for comprehensive logging
- **User Input**: Interactive credential entry via serial terminal
- **Status Monitoring**: Real-time application status and diagnostics
- **Command Interface**: Runtime commands for status and control

## Hardware Setup

### Connections

#### EC200U-EU to BL0942 UART Connection
```
EC200U-EU Pin    | BL0942 Pin     | Description
-----------------|----------------|-------------
UART2_TXD        | RX/SDI (Pin 9) | UART Transmit
UART2_RXD        | TX/SDO (Pin 10)| UART Receive
GND              | GND (Pin 5)    | Ground
3V3_OUT          | VDD (Pin 1)    | Power Supply
GND              | SEL (Pin 7)    | UART Mode Select
```

#### Debug UART Connection
```
EC200U-EU Pin    | Debug Interface| Description
-----------------|----------------|-------------
UART1_TXD        | USB-Serial RX  | Debug Output
UART1_RXD        | USB-Serial TX  | User Input
GND              | USB-Serial GND | Ground
```

#### BL0942 Power and Sensing
```
BL0942 Pin       | Connection     | Description
-----------------|----------------|-------------
VP (Pin 4)       | Voltage Sense  | Line voltage input (scaled)
IP (Pin 2)       | Shunt +        | Current sensing positive
IN (Pin 3)       | Shunt -        | Current sensing negative
VDD (Pin 1)      | 3.3V           | Power supply
GND (Pin 5)      | Ground         | Ground reference
```

### Required Components
- EC200U-EU cellular module
- BL0942 energy monitoring IC (SSOP10L package)
- 1mΩ current shunt resistor
- Voltage divider for line voltage sensing
- USB-to-Serial adapter for debug interface
- SIM card with data plan
- External cellular antenna

## Software Architecture

### Task Structure
1. **Main Task**: Application state machine and coordination
2. **Network Task**: Cellular network management and monitoring
3. **MQTT Task**: MQTT client management and data publishing
4. **BL0942 Task**: Energy data sampling and processing
5. **User Input Task**: Debug interface and user interaction

### Application States
```
INIT → CONFIG_INPUT → NETWORK_SETUP → NETWORK_CONNECTING →
MQTT_CONNECTING → RUNNING ⇄ RECONNECTING → ERROR
```

### Data Flow
```
BL0942 → UART2 → Energy Data → JSON → MQTT → Cellular → Internet → Broker
                                ↓
Debug UART ← Status/Logs ← Application Tasks
```

## Configuration

### Network Configuration
- **APN**: atmirancell (fixed)
- **Username**: User-provided via serial terminal
- **Password**: User-provided via serial terminal
- **Authentication**: PAP
- **IP Type**: IPv4

### MQTT Configuration
```c
#define MQTT_SERVER_IP      "**************"
#define MQTT_SERVER_PORT    1883
#define MQTT_USERNAME       "admin"
#define MQTT_PASSWORD       "admin"
#define MQTT_TOPIC          "EnergyMeter1"
#define DEVICE_CLIENT_ID    "12345678"
```

### Timing Configuration
```c
#define ENERGY_SAMPLE_INTERVAL_MS    5000    // 5 seconds
#define MQTT_PUBLISH_INTERVAL_MS     10000   // 10 seconds
#define NETWORK_CHECK_INTERVAL_MS    30000   // 30 seconds
#define STATUS_REPORT_INTERVAL_MS    60000   // 60 seconds
```

## Usage Instructions

### 1. Hardware Setup
1. Connect BL0942 to EC200U-EU as per connection diagram
2. Install current shunt resistor (1mΩ recommended)
3. Connect voltage sensing circuit with appropriate scaling
4. Connect debug UART to USB-Serial adapter
5. Install SIM card and cellular antenna
6. Power up the system with stable 3.3V supply

### 2. Initial Configuration
1. Open serial terminal (115200 baud, 8N1)
2. Power on the EC200U-EU module
3. Wait for initialization messages
4. When prompted, enter APN username
5. When prompted, enter APN password
6. System will automatically proceed with network connection

### 3. Operation Monitoring
The debug terminal will display:
- Network registration status
- Signal strength and technology (LTE/3G/EDGE)
- MQTT connection status
- Energy measurement data
- Error messages and recovery attempts
- Periodic status reports

### 4. Runtime Commands
While running, you can send commands via debug UART:
- `s` or `S`: Display current status
- `r` or `R`: Reset application

## JSON Data Format

The system publishes energy data in JSON format:

```json
{
  "device_id": "12345678",
  "timestamp": 1234567890,
  "voltage_rms": 230000,
  "current_rms": 1500,
  "power": 345000,
  "energy_count": 12345,
  "frequency": 5000,
  "status": 0,
  "valid": true
}
```

### Data Units
- **voltage_rms**: millivolts (mV)
- **current_rms**: milliamperes (mA)
- **power**: milliwatts (mW)
- **energy_count**: pulse count (unitless)
- **frequency**: Hz × 100 (5000 = 50.00 Hz)
- **timestamp**: milliseconds since boot
- **status**: BL0942 status register value

## Debug Output Examples

### Startup Sequence
```
========================================
  BL0942 MQTT Energy Monitor v1.0.0
  EC200U-EU Cellular Module
  Client ID: 12345678
========================================

=== DEBUG UART INITIALIZED ===
Initializing application...
Initializing network subsystem...
Initializing MQTT subsystem...
Initializing BL0942 communication...
BL0942 UART opened: Port=2, Baud=4800
BL0942 communication test successful, status: 0x000000
Application started successfully

=== APN CONFIGURATION ===
APN Name: atmirancell (fixed)
Please enter APN username: myusername
Username received: myusername
Please enter APN password: mypassword
Password received: mypassword
Configuration complete!
========================
```

### Network Connection
```
Starting network registration...
SIM card ready
Network selection set to automatic
Waiting for network registration...
Network registration callback: state=1
Network: Registered (Home)
Network registered, starting data call...
Configuring data call...
APN: atmirancell
Username: myusername
Password: mypassword
Data call started
Data call: CONNECTED
IP Address: ************
Network connected, proceeding to MQTT connection
```

### MQTT Connection and Data Publishing
```
Attempting MQTT connection...
Connecting to MQTT broker...
Server: **************:1883
Client ID: 12345678
Username: admin
Topic: EnergyMeter1
MQTT Event: CONNECTED
MQTT connected, application now running

Reading BL0942 energy data...
BL0942 packet read successfully
Parsed BL0942 packet: I=1234, V=230000, P=283620, E=5678, F=20000
Converted values: I=1234mA, V=230000mV, P=283620mW, F=50.00Hz
Energy data updated (sample #1)

Publishing energy data to MQTT...
Publishing to topic: EnergyMeter1
Payload: {"device_id":"12345678","timestamp":12345,"voltage_rms":230000,"current_rms":1234,"power":283620,"energy_count":5678,"frequency":5000,"status":0,"valid":true}
MQTT Event: MESSAGE SENT
MQTT message published successfully
```

### Status Reports
```
=== APPLICATION STATUS ===
State: 5
Uptime: 120 seconds
Errors: 0
Samples: 24

--- NETWORK STATUS ---
Registered: YES
Data Connected: YES
Technology: LTE
RSSI: -75 dBm
Signal Quality: 25
Operator: Turkcell
Cell ID: 0x12345678
LAC: 0x1234

--- MQTT STATUS ---
Connected: YES
Messages Sent: 12
Messages Received: 0
Connection Attempts: 1
Last Publish: 2000 ms ago

--- ENERGY DATA ---
Voltage: 230.000 V
Current: 1.234 A
Power: 283.620 W
Energy Count: 5678
Frequency: 50.00 Hz
Status: 0x0000
Timestamp: 12345 ms
==========================
```

## Troubleshooting

### Common Issues

1. **SIM Card Not Ready**
   - Check SIM card installation
   - Verify SIM card is activated
   - Check PIN code requirements

2. **Network Registration Failed**
   - Check antenna connection
   - Verify signal strength
   - Check APN credentials
   - Ensure data plan is active

3. **MQTT Connection Failed**
   - Verify internet connectivity
   - Check MQTT broker accessibility
   - Verify credentials and client ID
   - Check firewall settings

4. **BL0942 Communication Errors**
   - Check UART connections
   - Verify baud rate settings
   - Check power supply stability
   - Ensure proper grounding

### Error Recovery
The system includes automatic error recovery:
- Network reconnection on connection loss
- MQTT reconnection on broker disconnection
- BL0942 communication retry on errors
- Application restart on critical failures

### Debug Commands
Use the debug UART interface to:
- Monitor real-time status
- Check error conditions
- Force application restart
- View detailed logs

## Performance Specifications

- **Sampling Rate**: 5 seconds (configurable)
- **Publishing Rate**: 10 seconds (configurable)
- **Network Check**: 30 seconds
- **Status Report**: 60 seconds
- **MQTT QoS**: Level 1 (at least once delivery)
- **Memory Usage**: ~32KB RAM, ~64KB Flash
- **Power Consumption**: ~200mA @ 3.3V (typical)

## License

This project is provided for educational and development purposes. Ensure compliance with local regulations regarding energy monitoring and cellular communications.

## Support

For technical support:
- Review BL0942 datasheet for IC specifications
- Consult EC200U-EU documentation for cellular features
- Check Quectel SDK documentation for API details
- Monitor debug UART output for diagnostic information
