# BL0942 MQTT Energy Monitor CMakeLists.txt

set(target bl0942_mqtt_client)

# Source files
set(${target}_src
    mqtt_energy_monitor.c
    debug_utils.c
    network_manager.c
    mqtt_client.c
    bl0942_comm.c
)

# Include directories
set(${target}_inc
    inc
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Create the executable
add_executable(${target} ${${target}_src})

# Set include directories
target_include_directories(${target} PRIVATE ${${target}_inc})

# Link with required Quectel libraries
target_link_libraries(${target} 
    ql_api_common
    ql_api_osi
    ql_api_uart
    ql_api_nw
    ql_api_datacall
    ql_api_sim
    ql_api_mqtt_client
    ql_api_dev
)

# Compiler definitions
target_compile_definitions(${target} PRIVATE
    CONFIG_BL0942_MQTT_CLIENT=1
    CONFIG_UART_SUPPORT=1
    CONFIG_NETWORK_SUPPORT=1
    CONFIG_MQTT_CLIENT_SUPPORT=1
    CONFIG_DEBUG_UART=1
)

# Compiler options
target_compile_options(${target} PRIVATE
    -Wall
    -Wextra
    -Wno-unused-parameter
    -Wno-unused-variable
    -O2
    -g
)

# Install executable
install(TARGETS ${target}
        RUNTIME DESTINATION bin)
