/**
 * @file mqtt_energy_monitor.c
 * @brief Main MQTT Energy Monitor Application
 * @version 1.0
 * @date 2024
 */

#include "mqtt_energy_monitor.h"
#include "ql_api_osi.h"
#include "ql_api_dev.h"
#include "ql_log.h"
#include <stdio.h>
#include <string.h>
#include <stdarg.h>

#define LOG_TAG "MAIN"

/* Global application context */
static app_context_t g_app_ctx;
static bool g_app_initialized = false;

/**
 * @brief Application entry point
 */
void ql_appmain_entry(void)
{
    debug_init();
    
    debug_print("\n");
    debug_print("========================================\n");
    debug_print("  BL0942 MQTT Energy Monitor v%s\n", APP_VERSION);
    debug_print("  EC200U-EU Cellular Module\n");
    debug_print("  Client ID: %s\n", DEVICE_CLIENT_ID);
    debug_print("========================================\n");
    
    /* Initialize and start application */
    if (app_init() != 0) {
        debug_print("ERROR: Failed to initialize application\n");
        return;
    }
    
    if (app_start() != 0) {
        debug_print("ERROR: Failed to start application\n");
        app_stop();
        return;
    }
    
    debug_print("Application started successfully\n");
}

/**
 * @brief Initialize the application
 */
int app_init(void)
{
    if (g_app_initialized) {
        debug_print("Application already initialized\n");
        return 0;
    }
    
    debug_print("Initializing application...\n");
    
    /* Clear application context */
    memset(&g_app_ctx, 0, sizeof(app_context_t));
    
    /* Set initial state */
    g_app_ctx.current_state = APP_STATE_INIT;
    
    /* Create synchronization objects */
    if (ql_rtos_mutex_create(&g_app_ctx.data_mutex) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create data mutex\n");
        return -1;
    }
    
    if (ql_rtos_semaphore_create(&g_app_ctx.config_ready_sem, 0) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create config semaphore\n");
        ql_rtos_mutex_delete(g_app_ctx.data_mutex);
        return -1;
    }
    
    if (ql_rtos_semaphore_create(&g_app_ctx.network_ready_sem, 0) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create network semaphore\n");
        ql_rtos_semaphore_delete(g_app_ctx.config_ready_sem);
        ql_rtos_mutex_delete(g_app_ctx.data_mutex);
        return -1;
    }
    
    if (ql_rtos_semaphore_create(&g_app_ctx.mqtt_ready_sem, 0) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create MQTT semaphore\n");
        ql_rtos_semaphore_delete(g_app_ctx.network_ready_sem);
        ql_rtos_semaphore_delete(g_app_ctx.config_ready_sem);
        ql_rtos_mutex_delete(g_app_ctx.data_mutex);
        return -1;
    }
    
    /* Initialize subsystems */
    if (user_input_init() != 0) {
        debug_print("ERROR: Failed to initialize user input\n");
        return -1;
    }
    
    if (network_init() != 0) {
        debug_print("ERROR: Failed to initialize network\n");
        return -1;
    }
    
    if (mqtt_init() != 0) {
        debug_print("ERROR: Failed to initialize MQTT\n");
        return -1;
    }
    
    if (bl0942_init() != 0) {
        debug_print("ERROR: Failed to initialize BL0942\n");
        return -1;
    }
    
    g_app_initialized = true;
    debug_print("Application initialization complete\n");
    
    return 0;
}

/**
 * @brief Start the application
 */
int app_start(void)
{
    if (!g_app_initialized) {
        debug_print("ERROR: Application not initialized\n");
        return -1;
    }
    
    debug_print("Starting application tasks...\n");
    
    /* Create main task */
    if (ql_rtos_task_create(&g_app_ctx.main_task, MAIN_TASK_STACK_SIZE, 
                           MAIN_TASK_PRIORITY, "main_task", 
                           main_task_entry, NULL, 5) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create main task\n");
        return -1;
    }
    
    /* Create network task */
    if (ql_rtos_task_create(&g_app_ctx.network_task, NETWORK_TASK_STACK_SIZE, 
                           NETWORK_TASK_PRIORITY, "network_task", 
                           network_task_entry, NULL, 5) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create network task\n");
        ql_rtos_task_delete(g_app_ctx.main_task);
        return -1;
    }
    
    /* Create MQTT task */
    if (ql_rtos_task_create(&g_app_ctx.mqtt_task, MQTT_TASK_STACK_SIZE, 
                           MQTT_TASK_PRIORITY, "mqtt_task", 
                           mqtt_task_entry, NULL, 5) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create MQTT task\n");
        ql_rtos_task_delete(g_app_ctx.network_task);
        ql_rtos_task_delete(g_app_ctx.main_task);
        return -1;
    }
    
    /* Create BL0942 task */
    if (ql_rtos_task_create(&g_app_ctx.bl0942_task, BL0942_TASK_STACK_SIZE, 
                           BL0942_TASK_PRIORITY, "bl0942_task", 
                           bl0942_task_entry, NULL, 5) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create BL0942 task\n");
        ql_rtos_task_delete(g_app_ctx.mqtt_task);
        ql_rtos_task_delete(g_app_ctx.network_task);
        ql_rtos_task_delete(g_app_ctx.main_task);
        return -1;
    }
    
    debug_print("All tasks created successfully\n");
    return 0;
}

/**
 * @brief Stop the application
 */
int app_stop(void)
{
    if (!g_app_initialized) {
        return 0;
    }
    
    debug_print("Stopping application...\n");
    
    /* Update state */
    g_app_ctx.current_state = APP_STATE_ERROR;
    
    /* Disconnect MQTT */
    mqtt_disconnect();
    
    /* Delete tasks */
    if (g_app_ctx.bl0942_task) {
        ql_rtos_task_delete(g_app_ctx.bl0942_task);
    }
    if (g_app_ctx.mqtt_task) {
        ql_rtos_task_delete(g_app_ctx.mqtt_task);
    }
    if (g_app_ctx.network_task) {
        ql_rtos_task_delete(g_app_ctx.network_task);
    }
    if (g_app_ctx.main_task) {
        ql_rtos_task_delete(g_app_ctx.main_task);
    }
    
    /* Clean up synchronization objects */
    ql_rtos_semaphore_delete(g_app_ctx.mqtt_ready_sem);
    ql_rtos_semaphore_delete(g_app_ctx.network_ready_sem);
    ql_rtos_semaphore_delete(g_app_ctx.config_ready_sem);
    ql_rtos_mutex_delete(g_app_ctx.data_mutex);
    
    g_app_initialized = false;
    debug_print("Application stopped\n");
    
    return 0;
}

/**
 * @brief Get application context
 */
app_context_t* app_get_context(void)
{
    return &g_app_ctx;
}

/**
 * @brief Main task entry point
 */
void main_task_entry(void *param)
{
    debug_print("Main task started\n");
    
    uint32_t last_status_report = 0;
    uint32_t uptime_counter = 0;
    
    /* Start with configuration input */
    g_app_ctx.current_state = APP_STATE_CONFIG_INPUT;
    
    while (g_app_initialized) {
        uint32_t current_time = ql_rtos_up_time_ms();
        
        /* Update uptime counter */
        if (current_time - uptime_counter >= 1000) {
            g_app_ctx.uptime_seconds++;
            uptime_counter = current_time;
        }
        
        /* State machine */
        switch (g_app_ctx.current_state) {
            case APP_STATE_CONFIG_INPUT:
                debug_print("Waiting for user configuration...\n");
                debug_print("Please enter APN username and password via serial terminal\n");
                
                /* Wait for configuration to be ready */
                if (ql_rtos_semaphore_wait(g_app_ctx.config_ready_sem, 1000) == QL_OSI_SUCCESS) {
                    debug_print("Configuration received, proceeding to network setup\n");
                    g_app_ctx.current_state = APP_STATE_NETWORK_SETUP;
                }
                break;
                
            case APP_STATE_NETWORK_SETUP:
                debug_print("Setting up network connection...\n");
                g_app_ctx.current_state = APP_STATE_NETWORK_CONNECTING;
                break;
                
            case APP_STATE_NETWORK_CONNECTING:
                /* Wait for network to be ready */
                if (ql_rtos_semaphore_wait(g_app_ctx.network_ready_sem, 1000) == QL_OSI_SUCCESS) {
                    debug_print("Network connected, proceeding to MQTT connection\n");
                    g_app_ctx.current_state = APP_STATE_MQTT_CONNECTING;
                }
                break;
                
            case APP_STATE_MQTT_CONNECTING:
                /* Wait for MQTT to be ready */
                if (ql_rtos_semaphore_wait(g_app_ctx.mqtt_ready_sem, 1000) == QL_OSI_SUCCESS) {
                    debug_print("MQTT connected, application now running\n");
                    g_app_ctx.current_state = APP_STATE_RUNNING;
                }
                break;
                
            case APP_STATE_RUNNING:
                /* Normal operation - handled by other tasks */
                break;
                
            case APP_STATE_ERROR:
                debug_print("Application in error state, attempting recovery...\n");
                g_app_ctx.current_state = APP_STATE_RECONNECTING;
                break;
                
            case APP_STATE_RECONNECTING:
                /* Attempt to reconnect */
                delay_ms(5000); // Wait 5 seconds before retry
                g_app_ctx.current_state = APP_STATE_NETWORK_SETUP;
                break;
                
            default:
                debug_print("Unknown application state: %d\n", g_app_ctx.current_state);
                g_app_ctx.current_state = APP_STATE_ERROR;
                break;
        }
        
        /* Print status report every minute */
        if (current_time - last_status_report >= STATUS_REPORT_INTERVAL_MS) {
            debug_print_app_status();
            last_status_report = current_time;
        }
        
        /* Sleep for a short time */
        delay_ms(100);
    }
    
    debug_print("Main task exiting\n");
}

/**
 * @brief Print application status
 */
void debug_print_app_status(void)
{
    debug_print("\n=== APPLICATION STATUS ===\n");
    debug_print("State: %d\n", g_app_ctx.current_state);
    debug_print("Uptime: %u seconds\n", g_app_ctx.uptime_seconds);
    debug_print("Errors: %u\n", g_app_ctx.error_count);
    debug_print("Samples: %u\n", g_app_ctx.sample_count);
    
    debug_print_network_status(&g_app_ctx.network_status);
    debug_print_mqtt_status(&g_app_ctx.mqtt_status);
    debug_print_energy_data(&g_app_ctx.energy_data);
    
    debug_print("==========================\n\n");
}

/**
 * @brief Handle application errors
 */
void handle_error(const char *function, int error_code)
{
    g_app_ctx.error_count++;
    debug_print("ERROR in %s: %d (Total errors: %u)\n", 
                function, error_code, g_app_ctx.error_count);
    
    /* If too many errors, reset application */
    if (g_app_ctx.error_count > 10) {
        debug_print("Too many errors, resetting application\n");
        reset_application();
    }
}

/**
 * @brief Reset application
 */
void reset_application(void)
{
    debug_print("Resetting application...\n");
    g_app_ctx.current_state = APP_STATE_ERROR;
    g_app_ctx.error_count = 0;
    
    /* Reset network and MQTT connections */
    mqtt_disconnect();
    delay_ms(2000);
    
    g_app_ctx.current_state = APP_STATE_NETWORK_SETUP;
}

/**
 * @brief Get current timestamp
 */
uint32_t get_timestamp(void)
{
    return ql_rtos_up_time_ms();
}

/**
 * @brief Delay function
 */
void delay_ms(uint32_t ms)
{
    ql_rtos_task_sleep_ms(ms);
}
