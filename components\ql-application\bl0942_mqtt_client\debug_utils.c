/**
 * @file debug_utils.c
 * @brief Debug and User Input Utilities
 * @version 1.0
 * @date 2024
 */

#include "mqtt_energy_monitor.h"
#include "ql_api_uart.h"
#include "ql_log.h"
#include <stdio.h>
#include <string.h>
#include <stdarg.h>

#define DEBUG_LOG_TAG "DEBUG"

/* Debug UART configuration */
static ql_uart_config_t debug_uart_config;
static bool debug_initialized = false;

/* User input state */
static bool user_input_active = false;
static char input_buffer[128];
static uint8_t input_index = 0;

/**
 * @brief Initialize debug UART
 */
void debug_init(void)
{
    if (debug_initialized) {
        return;
    }
    
    /* Configure debug UART */
    debug_uart_config.baudrate = DEBUG_UART_BAUDRATE;
    debug_uart_config.data_bit = QL_UART_DATABIT_8;
    debug_uart_config.stop_bit = QL_UART_STOPBIT_1;
    debug_uart_config.parity_bit = QL_UART_PARITY_NONE;
    debug_uart_config.flow_ctrl = QL_UART_FC_NONE;
    
    /* Open debug UART */
    if (ql_uart_open(DEBUG_UART_PORT, &debug_uart_config) == QL_UART_SUCCESS) {
        debug_initialized = true;
        
        /* Send initial message */
        const char *init_msg = "\n\n=== DEBUG UART INITIALIZED ===\n";
        ql_uart_write(DEBUG_UART_PORT, (uint8_t*)init_msg, strlen(init_msg));
    }
}

/**
 * @brief Print debug message
 */
void debug_print(const char *format, ...)
{
    if (!debug_initialized) {
        return;
    }
    
    char buffer[256];
    va_list args;
    
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    if (len > 0 && len < sizeof(buffer)) {
        ql_uart_write(DEBUG_UART_PORT, (uint8_t*)buffer, len);
    }
    
    /* Also log to system */
    QL_LOG(QL_LOG_LEVEL_INFO, DEBUG_LOG_TAG, "%s", buffer);
}

/**
 * @brief Print network status
 */
void debug_print_network_status(const network_status_t *status)
{
    if (!status) {
        return;
    }
    
    debug_print("--- NETWORK STATUS ---\n");
    debug_print("Registered: %s\n", status->registered ? "YES" : "NO");
    debug_print("Data Connected: %s\n", status->data_connected ? "YES" : "NO");
    debug_print("Technology: %s\n", network_tech_to_string(status->technology));
    debug_print("RSSI: %d dBm\n", status->rssi);
    debug_print("Signal Quality: %u\n", status->signal_quality);
    debug_print("Operator: %s\n", status->operator_name);
    debug_print("Cell ID: 0x%08X\n", status->cell_id);
    debug_print("LAC: 0x%04X\n", status->lac);
}

/**
 * @brief Print MQTT status
 */
void debug_print_mqtt_status(const mqtt_status_t *status)
{
    if (!status) {
        return;
    }
    
    debug_print("--- MQTT STATUS ---\n");
    debug_print("Connected: %s\n", status->connected ? "YES" : "NO");
    debug_print("Messages Sent: %u\n", status->messages_sent);
    debug_print("Messages Received: %u\n", status->messages_received);
    debug_print("Connection Attempts: %u\n", status->connection_attempts);
    debug_print("Last Publish: %u ms ago\n", 
                get_timestamp() - status->last_publish_time);
    
    if (status->last_error_code != 0) {
        debug_print("Last Error: %d (%u ms ago)\n", 
                    status->last_error_code,
                    get_timestamp() - status->last_error_time);
    }
}

/**
 * @brief Print energy data
 */
void debug_print_energy_data(const bl0942_data_t *data)
{
    if (!data || !data->valid) {
        debug_print("--- ENERGY DATA ---\n");
        debug_print("Status: INVALID\n");
        return;
    }
    
    debug_print("--- ENERGY DATA ---\n");
    debug_print("Voltage: %u.%03u V\n", data->voltage_rms / 1000, data->voltage_rms % 1000);
    debug_print("Current: %u.%03u A\n", data->current_rms / 1000, data->current_rms % 1000);
    debug_print("Power: %d.%03u W\n", data->power / 1000, abs(data->power) % 1000);
    debug_print("Energy Count: %u\n", data->energy_count);
    debug_print("Frequency: %u.%02u Hz\n", data->frequency / 100, data->frequency % 100);
    debug_print("Status: 0x%04X\n", data->status);
    debug_print("Timestamp: %u ms\n", data->timestamp);
}

/**
 * @brief Initialize user input
 */
int user_input_init(void)
{
    debug_print("User input initialized\n");
    debug_print("Ready to receive APN credentials\n");
    
    /* Create user input task */
    ql_task_t user_input_task;
    if (ql_rtos_task_create(&user_input_task, 2048, 14, "user_input", 
                           user_input_task_entry, NULL, 5) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create user input task\n");
        return -1;
    }
    
    return 0;
}

/**
 * @brief Get user credentials
 */
int user_input_get_credentials(user_config_t *config)
{
    if (!config) {
        return -1;
    }
    
    debug_print("\n=== APN CONFIGURATION ===\n");
    debug_print("APN Name: %s (fixed)\n", APN_NAME);
    debug_print("Please enter APN username: ");
    
    user_input_active = true;
    input_index = 0;
    memset(input_buffer, 0, sizeof(input_buffer));
    
    /* Wait for username input */
    while (user_input_active) {
        delay_ms(100);
    }
    
    /* Copy username */
    strncpy(config->apn_username, input_buffer, MAX_USERNAME_LEN - 1);
    config->apn_username[MAX_USERNAME_LEN - 1] = '\0';
    
    debug_print("\nUsername received: %s\n", config->apn_username);
    debug_print("Please enter APN password: ");
    
    user_input_active = true;
    input_index = 0;
    memset(input_buffer, 0, sizeof(input_buffer));
    
    /* Wait for password input */
    while (user_input_active) {
        delay_ms(100);
    }
    
    /* Copy password */
    strncpy(config->apn_password, input_buffer, MAX_PASSWORD_LEN - 1);
    config->apn_password[MAX_PASSWORD_LEN - 1] = '\0';
    
    debug_print("\nPassword received: %s\n", config->apn_password);
    debug_print("Configuration complete!\n");
    debug_print("========================\n\n");
    
    config->config_complete = true;
    return 0;
}

/**
 * @brief User input task
 */
void user_input_task_entry(void *param)
{
    debug_print("User input task started\n");
    
    app_context_t *ctx = app_get_context();
    uint8_t rx_buffer[32];
    uint32_t received;
    
    while (true) {
        /* Check if we need to get user configuration */
        if (ctx->current_state == APP_STATE_CONFIG_INPUT && 
            !ctx->user_config.config_complete) {
            
            if (user_input_get_credentials(&ctx->user_config) == 0) {
                /* Signal that configuration is ready */
                ql_rtos_semaphore_release(ctx->config_ready_sem);
            }
        }
        
        /* Check for incoming data on debug UART */
        if (ql_uart_read(DEBUG_UART_PORT, rx_buffer, sizeof(rx_buffer)) > 0) {
            for (uint32_t i = 0; i < received; i++) {
                char ch = rx_buffer[i];
                
                if (user_input_active) {
                    if (ch == '\r' || ch == '\n') {
                        /* End of input */
                        input_buffer[input_index] = '\0';
                        user_input_active = false;
                        debug_print("\n");
                    } else if (ch == '\b' || ch == 127) {
                        /* Backspace */
                        if (input_index > 0) {
                            input_index--;
                            debug_print("\b \b");
                        }
                    } else if (ch >= 32 && ch <= 126 && input_index < sizeof(input_buffer) - 1) {
                        /* Printable character */
                        input_buffer[input_index++] = ch;
                        debug_print("%c", ch);
                    }
                } else {
                    /* Handle commands when not in input mode */
                    if (ch == 's' || ch == 'S') {
                        debug_print_app_status();
                    } else if (ch == 'r' || ch == 'R') {
                        debug_print("Resetting application...\n");
                        reset_application();
                    }
                }
            }
        }
        
        delay_ms(50);
    }
}

/**
 * @brief Validate user configuration
 */
int validate_config(const user_config_t *config)
{
    if (!config) {
        debug_print("ERROR: Invalid config pointer\n");
        return -1;
    }
    
    if (!config->config_complete) {
        debug_print("ERROR: Configuration not complete\n");
        return -1;
    }
    
    if (strlen(config->apn_username) == 0) {
        debug_print("ERROR: APN username is empty\n");
        return -1;
    }
    
    if (strlen(config->apn_password) == 0) {
        debug_print("ERROR: APN password is empty\n");
        return -1;
    }
    
    debug_print("Configuration validation passed\n");
    return 0;
}

/**
 * @brief Convert network technology to string
 */
const char* network_tech_to_string(network_tech_t tech)
{
    switch (tech) {
        case NETWORK_TECH_EDGE:
            return "EDGE";
        case NETWORK_TECH_3G:
            return "3G";
        case NETWORK_TECH_LTE:
            return "LTE";
        default:
            return "UNKNOWN";
    }
}
