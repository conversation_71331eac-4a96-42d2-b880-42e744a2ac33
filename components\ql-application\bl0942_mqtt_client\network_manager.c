/**
 * @file network_manager.c
 * @brief Network Management for EC200U-EU
 * @version 1.0
 * @date 2024
 */

#include "mqtt_energy_monitor.h"
#include "ql_api_nw.h"
#include "ql_api_datacall.h"
#include "ql_api_sim.h"
#include "ql_log.h"
#include <string.h>

#define NETWORK_LOG_TAG "NETWORK"

/* Network callback prototypes */
static void network_registration_callback(uint8_t nSim, unsigned int ind_type, void *ind_msg_buf);
static void data_call_callback(uint8_t nSim, unsigned int ind_type, void *ind_msg_buf);

/**
 * @brief Initialize network subsystem
 */
int network_init(void)
{
    debug_print("Initializing network subsystem...\n");
    
    /* Register network callbacks */
    if (ql_nw_register_cb(network_registration_callback) != QL_NW_SUCCESS) {
        debug_print("ERROR: Failed to register network callback\n");
        return -1;
    }
    
    if (ql_datacall_register_cb(data_call_callback) != QL_DATACALL_SUCCESS) {
        debug_print("ERROR: Failed to register data call callback\n");
        return -1;
    }
    
    debug_print("Network subsystem initialized\n");
    return 0;
}

/**
 * @brief Network task entry point
 */
void network_task_entry(void *param)
{
    debug_print("Network task started\n");
    
    app_context_t *ctx = app_get_context();
    uint32_t last_status_check = 0;
    
    while (true) {
        uint32_t current_time = get_timestamp();
        
        /* Handle network state based on application state */
        switch (ctx->current_state) {
            case APP_STATE_NETWORK_SETUP:
                debug_print("Starting network registration...\n");
                if (network_register() == 0) {
                    debug_print("Network registration initiated\n");
                } else {
                    debug_print("ERROR: Failed to start network registration\n");
                    handle_error("network_register", -1);
                    ctx->current_state = APP_STATE_ERROR;
                }
                break;
                
            case APP_STATE_NETWORK_CONNECTING:
                /* Wait for network registration and start data call */
                if (ctx->network_status.registered && !ctx->network_status.data_connected) {
                    debug_print("Network registered, starting data call...\n");
                    if (network_start_data_call() == 0) {
                        debug_print("Data call initiated\n");
                    } else {
                        debug_print("ERROR: Failed to start data call\n");
                        handle_error("network_start_data_call", -1);
                    }
                }
                break;
                
            case APP_STATE_RUNNING:
                /* Monitor network status */
                if (current_time - last_status_check >= NETWORK_CHECK_INTERVAL_MS) {
                    network_get_status(&ctx->network_status);
                    
                    /* Check if we lost connection */
                    if (!ctx->network_status.data_connected) {
                        debug_print("WARNING: Data connection lost, attempting reconnection\n");
                        ctx->current_state = APP_STATE_RECONNECTING;
                    }
                    
                    last_status_check = current_time;
                }
                break;
                
            default:
                break;
        }
        
        delay_ms(1000);
    }
}

/**
 * @brief Register to network
 */
int network_register(void)
{
    debug_print("Checking SIM card status...\n");
    
    /* Check SIM card status */
    ql_sim_status_e sim_status;
    if (ql_sim_get_card_status(&sim_status, 0) != QL_SIM_SUCCESS) {
        debug_print("ERROR: Failed to get SIM card status\n");
        return -1;
    }
    
    if (sim_status != QL_SIM_STATUS_READY) {
        debug_print("ERROR: SIM card not ready, status: %d\n", sim_status);
        return -1;
    }
    
    debug_print("SIM card ready\n");
    
    /* Set automatic network selection */
    if (ql_nw_set_selection(QL_NW_SELECTION_AUTO_MODE, NULL, 0) != QL_NW_SUCCESS) {
        debug_print("ERROR: Failed to set automatic network selection\n");
        return -1;
    }
    
    debug_print("Network selection set to automatic\n");
    debug_print("Waiting for network registration...\n");
    
    return 0;
}

/**
 * @brief Start data call
 */
int network_start_data_call(void)
{
    app_context_t *ctx = app_get_context();
    
    debug_print("Configuring data call...\n");
    debug_print("APN: %s\n", APN_NAME);
    debug_print("Username: %s\n", ctx->user_config.apn_username);
    debug_print("Password: %s\n", ctx->user_config.apn_password);
    
    /* Configure data call profile */
    ql_data_call_info_s profile_info;
    memset(&profile_info, 0, sizeof(profile_info));
    
    profile_info.profile_idx = 1;
    profile_info.ip_version = QL_PDP_TYPE_IP;
    profile_info.auth_type = QL_PDP_AUTH_PAP;
    
    strncpy(profile_info.apn, APN_NAME, sizeof(profile_info.apn) - 1);
    strncpy(profile_info.username, ctx->user_config.apn_username, sizeof(profile_info.username) - 1);
    strncpy(profile_info.password, ctx->user_config.apn_password, sizeof(profile_info.password) - 1);
    
    /* Set data call configuration */
    if (ql_set_data_call_info(0, 1, &profile_info) != QL_DATACALL_SUCCESS) {
        debug_print("ERROR: Failed to set data call info\n");
        return -1;
    }
    
    /* Start data call */
    if (ql_start_data_call(0, 1, QL_PDP_TYPE_IP) != QL_DATACALL_SUCCESS) {
        debug_print("ERROR: Failed to start data call\n");
        return -1;
    }
    
    debug_print("Data call started\n");
    return 0;
}

/**
 * @brief Get network status
 */
int network_get_status(network_status_t *status)
{
    if (!status) {
        return -1;
    }
    
    /* Get registration status */
    ql_nw_reg_status_info_s reg_info;
    if (ql_nw_get_reg_status(&reg_info, 0) == QL_NW_SUCCESS) {
        status->registered = (reg_info.state == QL_NW_REG_STATE_REGISTERED_HOME ||
                             reg_info.state == QL_NW_REG_STATE_REGISTERED_ROAMING);
        status->cell_id = reg_info.cid;
        status->lac = reg_info.lac;
    }
    
    /* Get operator name */
    ql_nw_operator_info_s operator_info;
    if (ql_nw_get_operator_name(&operator_info, 0) == QL_NW_SUCCESS) {
        strncpy(status->operator_name, operator_info.long_oper_name, 
                sizeof(status->operator_name) - 1);
    }
    
    /* Get signal strength */
    ql_nw_signal_strength_info_s signal_info;
    if (ql_nw_get_signal_strength(&signal_info, 0) == QL_NW_SUCCESS) {
        status->rssi = signal_info.rssi;
        status->signal_quality = signal_info.rsrq;
    }
    
    /* Get network technology */
    ql_nw_nitz_time_info_s nitz_info;
    if (ql_nw_get_nitz_time_info(&nitz_info, 0) == QL_NW_SUCCESS) {
        switch (nitz_info.act) {
            case QL_NW_ACCESS_TECH_GSM:
            case QL_NW_ACCESS_TECH_GSM_COMPACT:
                status->technology = NETWORK_TECH_EDGE;
                break;
            case QL_NW_ACCESS_TECH_UTRAN:
                status->technology = NETWORK_TECH_3G;
                break;
            case QL_NW_ACCESS_TECH_E_UTRAN:
                status->technology = NETWORK_TECH_LTE;
                break;
            default:
                status->technology = NETWORK_TECH_UNKNOWN;
                break;
        }
    }
    
    /* Check data call status */
    ql_data_call_info_s data_call_info;
    if (ql_get_data_call_info(0, 1, &data_call_info) == QL_DATACALL_SUCCESS) {
        status->data_connected = (data_call_info.ip_version != 0);
    }
    
    return 0;
}

/**
 * @brief Network registration callback
 */
static void network_registration_callback(uint8_t nSim, unsigned int ind_type, void *ind_msg_buf)
{
    app_context_t *ctx = app_get_context();
    ql_nw_common_reg_status_info_s *reg_info = (ql_nw_common_reg_status_info_s *)ind_msg_buf;
    
    if (!reg_info) {
        return;
    }
    
    debug_print("Network registration callback: state=%d\n", reg_info->state);
    
    switch (reg_info->state) {
        case QL_NW_REG_STATE_NOT_REGISTERED:
            debug_print("Network: Not registered\n");
            ctx->network_status.registered = false;
            break;
            
        case QL_NW_REG_STATE_REGISTERED_HOME:
            debug_print("Network: Registered (Home)\n");
            ctx->network_status.registered = true;
            ctx->network_status.cell_id = reg_info->cid;
            ctx->network_status.lac = reg_info->lac;
            break;
            
        case QL_NW_REG_STATE_SEARCHING:
            debug_print("Network: Searching...\n");
            ctx->network_status.registered = false;
            break;
            
        case QL_NW_REG_STATE_DENIED:
            debug_print("Network: Registration denied\n");
            ctx->network_status.registered = false;
            handle_error("network_registration", reg_info->state);
            break;
            
        case QL_NW_REG_STATE_REGISTERED_ROAMING:
            debug_print("Network: Registered (Roaming)\n");
            ctx->network_status.registered = true;
            ctx->network_status.cell_id = reg_info->cid;
            ctx->network_status.lac = reg_info->lac;
            break;
            
        default:
            debug_print("Network: Unknown state %d\n", reg_info->state);
            break;
    }
    
    /* Update network status */
    network_get_status(&ctx->network_status);
}

/**
 * @brief Data call callback
 */
static void data_call_callback(uint8_t nSim, unsigned int ind_type, void *ind_msg_buf)
{
    app_context_t *ctx = app_get_context();
    ql_data_call_info_s *call_info = (ql_data_call_info_s *)ind_msg_buf;
    
    if (!call_info) {
        return;
    }
    
    debug_print("Data call callback: profile=%d, type=%d\n", 
                call_info->profile_idx, ind_type);
    
    if (call_info->profile_idx == 1) {
        switch (ind_type) {
            case QL_DATACALL_CONNECT_IND:
                debug_print("Data call: CONNECTED\n");
                ctx->network_status.data_connected = true;
                
                /* Get IP address */
                if (call_info->ip_version == QL_PDP_TYPE_IP) {
                    uint32_t ip = call_info->v4.addr.ip;
                    debug_print("IP Address: %d.%d.%d.%d\n",
                                (ip & 0xFF), ((ip >> 8) & 0xFF),
                                ((ip >> 16) & 0xFF), ((ip >> 24) & 0xFF));
                }
                
                /* Signal that network is ready */
                ql_rtos_semaphore_release(ctx->network_ready_sem);
                break;
                
            case QL_DATACALL_DISCONNECT_IND:
                debug_print("Data call: DISCONNECTED\n");
                ctx->network_status.data_connected = false;
                
                /* If we were running, go to reconnecting state */
                if (ctx->current_state == APP_STATE_RUNNING) {
                    ctx->current_state = APP_STATE_RECONNECTING;
                }
                break;
                
            default:
                debug_print("Data call: Unknown indication %d\n", ind_type);
                break;
        }
    }
}
