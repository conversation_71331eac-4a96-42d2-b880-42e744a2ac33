/**
 * @file mqtt_client.c
 * @brief MQTT Client Implementation
 * @version 1.0
 * @date 2024
 */

#include "mqtt_energy_monitor.h"
#include "ql_api_mqtt_client.h"
#include "ql_log.h"
#include <string.h>
#include <stdio.h>

#define MQTT_LOG_TAG "MQTT"

/* MQTT client handle */
static ql_mqtt_client_t mqtt_client_handle = NULL;

/**
 * @brief Initialize MQTT subsystem
 */
int mqtt_init(void)
{
    debug_print("Initializing MQTT subsystem...\n");
    
    /* Create MQTT client */
    if (ql_mqtt_client_init(&mqtt_client_handle) != QL_MQTT_CLIENT_SUCCESS) {
        debug_print("ERROR: Failed to initialize MQTT client\n");
        return -1;
    }
    
    app_context_t *ctx = app_get_context();
    ctx->mqtt_client = mqtt_client_handle;
    
    debug_print("MQTT subsystem initialized\n");
    return 0;
}

/**
 * @brief MQTT task entry point
 */
void mqtt_task_entry(void *param)
{
    debug_print("MQTT task started\n");
    
    app_context_t *ctx = app_get_context();
    uint32_t last_publish_time = 0;
    uint32_t connection_retry_time = 0;
    bool connection_attempted = false;
    
    while (true) {
        uint32_t current_time = get_timestamp();
        
        /* Handle MQTT state based on application state */
        switch (ctx->current_state) {
            case APP_STATE_MQTT_CONNECTING:
                if (!connection_attempted || 
                    (current_time - connection_retry_time >= 10000)) { // Retry every 10 seconds
                    
                    debug_print("Attempting MQTT connection...\n");
                    if (mqtt_connect() == 0) {
                        debug_print("MQTT connection initiated\n");
                        connection_attempted = true;
                        connection_retry_time = current_time;
                    } else {
                        debug_print("ERROR: Failed to initiate MQTT connection\n");
                        handle_error("mqtt_connect", -1);
                        connection_retry_time = current_time;
                    }
                }
                break;
                
            case APP_STATE_RUNNING:
                /* Publish energy data periodically */
                if (ctx->mqtt_status.connected && 
                    ctx->energy_data.valid &&
                    (current_time - last_publish_time >= MQTT_PUBLISH_INTERVAL_MS)) {
                    
                    debug_print("Publishing energy data to MQTT...\n");
                    if (mqtt_publish_energy_data(&ctx->energy_data) == 0) {
                        debug_print("Energy data published successfully\n");
                        ctx->mqtt_status.messages_sent++;
                        ctx->mqtt_status.last_publish_time = current_time;
                        last_publish_time = current_time;
                    } else {
                        debug_print("ERROR: Failed to publish energy data\n");
                        handle_error("mqtt_publish_energy_data", -1);
                    }
                }
                
                /* Check connection status */
                if (!ctx->mqtt_status.connected) {
                    debug_print("MQTT connection lost, attempting reconnection\n");
                    ctx->current_state = APP_STATE_MQTT_CONNECTING;
                    connection_attempted = false;
                }
                break;
                
            case APP_STATE_RECONNECTING:
                /* Disconnect and prepare for reconnection */
                if (ctx->mqtt_status.connected) {
                    mqtt_disconnect();
                }
                ctx->current_state = APP_STATE_MQTT_CONNECTING;
                connection_attempted = false;
                break;
                
            default:
                break;
        }
        
        delay_ms(1000);
    }
}

/**
 * @brief Connect to MQTT broker
 */
int mqtt_connect(void)
{
    app_context_t *ctx = app_get_context();
    
    if (!ctx->network_status.data_connected) {
        debug_print("ERROR: No data connection available for MQTT\n");
        return -1;
    }
    
    debug_print("Connecting to MQTT broker...\n");
    debug_print("Server: %s:%d\n", MQTT_SERVER_IP, MQTT_SERVER_PORT);
    debug_print("Client ID: %s\n", DEVICE_CLIENT_ID);
    debug_print("Username: %s\n", MQTT_USERNAME);
    debug_print("Topic: %s\n", MQTT_TOPIC);
    
    /* Configure MQTT connection parameters */
    ql_mqtt_connect_info_t connect_info;
    memset(&connect_info, 0, sizeof(connect_info));
    
    connect_info.keep_alive = MQTT_KEEPALIVE;
    connect_info.clean_session = 1;
    connect_info.will_flag = 0;
    
    strncpy(connect_info.client_id, DEVICE_CLIENT_ID, sizeof(connect_info.client_id) - 1);
    strncpy(connect_info.username, MQTT_USERNAME, sizeof(connect_info.username) - 1);
    strncpy(connect_info.password, MQTT_PASSWORD, sizeof(connect_info.password) - 1);
    
    /* Set server information */
    strncpy(connect_info.host, MQTT_SERVER_IP, sizeof(connect_info.host) - 1);
    connect_info.port = MQTT_SERVER_PORT;
    
    /* Register event callback */
    if (ql_mqtt_set_event_cb(ctx->mqtt_client, mqtt_event_callback, ctx) != QL_MQTT_CLIENT_SUCCESS) {
        debug_print("ERROR: Failed to set MQTT event callback\n");
        return -1;
    }
    
    /* Connect to broker */
    ctx->mqtt_status.connection_attempts++;
    if (ql_mqtt_connect(ctx->mqtt_client, &connect_info) != QL_MQTT_CLIENT_SUCCESS) {
        debug_print("ERROR: Failed to connect to MQTT broker\n");
        ctx->mqtt_status.last_error_code = -1;
        ctx->mqtt_status.last_error_time = get_timestamp();
        return -1;
    }
    
    debug_print("MQTT connection request sent\n");
    return 0;
}

/**
 * @brief Disconnect from MQTT broker
 */
int mqtt_disconnect(void)
{
    app_context_t *ctx = app_get_context();
    
    if (!ctx->mqtt_status.connected) {
        return 0;
    }
    
    debug_print("Disconnecting from MQTT broker...\n");
    
    if (ql_mqtt_disconnect(ctx->mqtt_client) != QL_MQTT_CLIENT_SUCCESS) {
        debug_print("ERROR: Failed to disconnect from MQTT broker\n");
        return -1;
    }
    
    ctx->mqtt_status.connected = false;
    debug_print("MQTT disconnected\n");
    
    return 0;
}

/**
 * @brief Publish energy data to MQTT
 */
int mqtt_publish_energy_data(const bl0942_data_t *data)
{
    if (!data || !data->valid) {
        debug_print("ERROR: Invalid energy data\n");
        return -1;
    }
    
    app_context_t *ctx = app_get_context();
    
    if (!ctx->mqtt_status.connected) {
        debug_print("ERROR: MQTT not connected\n");
        return -1;
    }
    
    /* Format JSON payload */
    if (format_energy_json(data, ctx->json_buffer, sizeof(ctx->json_buffer)) != 0) {
        debug_print("ERROR: Failed to format JSON payload\n");
        return -1;
    }
    
    debug_print("Publishing to topic: %s\n", MQTT_TOPIC);
    debug_print("Payload: %s\n", ctx->json_buffer);
    
    /* Publish message */
    ql_mqtt_publish_info_t publish_info;
    memset(&publish_info, 0, sizeof(publish_info));
    
    strncpy(publish_info.topic, MQTT_TOPIC, sizeof(publish_info.topic) - 1);
    publish_info.payload = (uint8_t*)ctx->json_buffer;
    publish_info.payload_len = strlen(ctx->json_buffer);
    publish_info.qos = MQTT_QOS;
    publish_info.retain = MQTT_RETAIN;
    
    if (ql_mqtt_publish(ctx->mqtt_client, &publish_info) != QL_MQTT_CLIENT_SUCCESS) {
        debug_print("ERROR: Failed to publish MQTT message\n");
        ctx->mqtt_status.last_error_code = -2;
        ctx->mqtt_status.last_error_time = get_timestamp();
        return -1;
    }
    
    debug_print("MQTT message published successfully\n");
    return 0;
}

/**
 * @brief MQTT event callback
 */
void mqtt_event_callback(ql_mqtt_client_t *client, void *user_data, ql_mqtt_event_info_t *event_info)
{
    app_context_t *ctx = (app_context_t*)user_data;
    
    if (!ctx || !event_info) {
        return;
    }
    
    switch (event_info->event) {
        case QL_MQTT_EVENT_CONNECT:
            debug_print("MQTT Event: CONNECTED\n");
            ctx->mqtt_status.connected = true;
            ctx->mqtt_status.last_error_code = 0;
            
            /* Signal that MQTT is ready */
            ql_rtos_semaphore_release(ctx->mqtt_ready_sem);
            break;
            
        case QL_MQTT_EVENT_DISCONNECT:
            debug_print("MQTT Event: DISCONNECTED\n");
            ctx->mqtt_status.connected = false;
            
            /* If we were running, attempt reconnection */
            if (ctx->current_state == APP_STATE_RUNNING) {
                ctx->current_state = APP_STATE_RECONNECTING;
            }
            break;
            
        case QL_MQTT_EVENT_PUBLISH_RECV:
            debug_print("MQTT Event: MESSAGE RECEIVED\n");
            ctx->mqtt_status.messages_received++;
            
            if (event_info->msg.publish_recv_info.topic && 
                event_info->msg.publish_recv_info.payload) {
                debug_print("Topic: %s\n", event_info->msg.publish_recv_info.topic);
                debug_print("Payload: %.*s\n", 
                           event_info->msg.publish_recv_info.payload_len,
                           (char*)event_info->msg.publish_recv_info.payload);
            }
            break;
            
        case QL_MQTT_EVENT_PUBLISH_SENT:
            debug_print("MQTT Event: MESSAGE SENT\n");
            break;
            
        case QL_MQTT_EVENT_ERROR:
            debug_print("MQTT Event: ERROR (code: %d)\n", event_info->msg.error.error_code);
            ctx->mqtt_status.last_error_code = event_info->msg.error.error_code;
            ctx->mqtt_status.last_error_time = get_timestamp();
            ctx->mqtt_status.connected = false;
            break;
            
        default:
            debug_print("MQTT Event: UNKNOWN (%d)\n", event_info->event);
            break;
    }
}

/**
 * @brief Format energy data as JSON
 */
int format_energy_json(const bl0942_data_t *data, char *buffer, size_t buffer_size)
{
    if (!data || !buffer || buffer_size == 0) {
        return -1;
    }
    
    int len = snprintf(buffer, buffer_size,
        "{"
        "\"device_id\":\"%s\","
        "\"timestamp\":%u,"
        "\"voltage_rms\":%u,"
        "\"current_rms\":%u,"
        "\"power\":%d,"
        "\"energy_count\":%u,"
        "\"frequency\":%u,"
        "\"status\":%u,"
        "\"valid\":%s"
        "}",
        DEVICE_CLIENT_ID,
        data->timestamp,
        data->voltage_rms,
        data->current_rms,
        data->power,
        data->energy_count,
        data->frequency,
        data->status,
        data->valid ? "true" : "false"
    );
    
    if (len >= buffer_size) {
        debug_print("ERROR: JSON buffer too small\n");
        return -1;
    }
    
    return 0;
}
