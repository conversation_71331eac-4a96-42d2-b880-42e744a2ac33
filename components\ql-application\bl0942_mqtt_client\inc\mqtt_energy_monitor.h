/**
 * @file mqtt_energy_monitor.h
 * @brief MQTT Energy Monitor Header for EC200U-EU with BL0942
 * @version 1.0
 * @date 2024
 *
 * Complete solution for BL0942 energy monitoring with MQTT over cellular networks
 */

#ifndef MQTT_ENERGY_MONITOR_H
#define MQTT_ENERGY_MONITOR_H

#include "ql_api_osi.h"
#include "ql_api_uart.h"
#include "ql_api_nw.h"
#include "ql_api_datacall.h"
#include "ql_api_mqtt_client.h"
#include "ql_log.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Application Configuration */
#define APP_VERSION                     "1.0.0"
#define DEVICE_CLIENT_ID               "12345678"

/* MQTT Configuration */
#define MQTT_SERVER_IP                 "**************"
#define MQTT_SERVER_PORT               1883
#define MQTT_USERNAME                  "admin"
#define MQTT_PASSWORD                  "admin"
#define MQTT_TOPIC                     "EnergyMeter1"
#define MQTT_KEEPALIVE                 60
#define MQTT_QOS                       1
#define MQTT_RETAIN                    0

/* Network Configuration */
#define APN_NAME                       "atmirancell"
#define MAX_USERNAME_LEN               32
#define MAX_PASSWORD_LEN               32
#define NETWORK_TIMEOUT_MS             60000
#define DATA_CALL_TIMEOUT_MS           30000

/* BL0942 UART Configuration */
#define BL0942_UART_PORT               QL_UART_PORT_2
#define BL0942_UART_BAUDRATE           QL_UART_BAUD_4800
#define BL0942_UART_DATABIT            QL_UART_DATABIT_8
#define BL0942_UART_STOPBIT            QL_UART_STOPBIT_1
#define BL0942_UART_PARITY             QL_UART_PARITY_NONE
#define BL0942_UART_FLOWCTRL           QL_UART_FC_NONE

/* Debug UART Configuration */
#define DEBUG_UART_PORT                QL_UART_PORT_1
#define DEBUG_UART_BAUDRATE            QL_UART_BAUD_115200

/* Task Configuration */
#define MAIN_TASK_STACK_SIZE           8192
#define MAIN_TASK_PRIORITY             10
#define NETWORK_TASK_STACK_SIZE        4096
#define NETWORK_TASK_PRIORITY          11
#define MQTT_TASK_STACK_SIZE           4096
#define MQTT_TASK_PRIORITY             12
#define BL0942_TASK_STACK_SIZE         4096
#define BL0942_TASK_PRIORITY           13

/* Timing Configuration */
#define ENERGY_SAMPLE_INTERVAL_MS      5000    // 5 seconds
#define MQTT_PUBLISH_INTERVAL_MS       10000   // 10 seconds
#define NETWORK_CHECK_INTERVAL_MS      30000   // 30 seconds
#define STATUS_REPORT_INTERVAL_MS      60000   // 60 seconds

/* Buffer Sizes */
#define JSON_BUFFER_SIZE               512
#define UART_RX_BUFFER_SIZE            256
#define UART_TX_BUFFER_SIZE            256
#define AT_RESPONSE_BUFFER_SIZE        512

/* Application States */
typedef enum {
    APP_STATE_INIT = 0,
    APP_STATE_CONFIG_INPUT,
    APP_STATE_NETWORK_SETUP,
    APP_STATE_NETWORK_CONNECTING,
    APP_STATE_MQTT_CONNECTING,
    APP_STATE_RUNNING,
    APP_STATE_ERROR,
    APP_STATE_RECONNECTING
} app_state_t;

/* Network Technology Types */
typedef enum {
    NETWORK_TECH_UNKNOWN = 0,
    NETWORK_TECH_EDGE,
    NETWORK_TECH_3G,
    NETWORK_TECH_LTE
} network_tech_t;

/* Network Status */
typedef struct {
    bool registered;
    bool data_connected;
    network_tech_t technology;
    int16_t rssi;
    uint8_t signal_quality;
    char operator_name[32];
    uint32_t cell_id;
    uint16_t lac;
} network_status_t;

/* MQTT Status */
typedef struct {
    bool connected;
    uint32_t messages_sent;
    uint32_t messages_received;
    uint32_t connection_attempts;
    uint32_t last_publish_time;
    uint32_t last_error_time;
    int last_error_code;
} mqtt_status_t;

/* BL0942 Energy Data */
typedef struct {
    uint32_t voltage_rms;       // mV
    uint32_t current_rms;       // mA
    int32_t power;              // mW
    uint32_t energy_count;      // Pulse count
    uint16_t frequency;         // Hz * 100
    uint16_t status;            // Status register
    uint32_t timestamp;         // System timestamp
    bool valid;                 // Data validity flag
} bl0942_data_t;

/* User Configuration */
typedef struct {
    char apn_username[MAX_USERNAME_LEN];
    char apn_password[MAX_PASSWORD_LEN];
    bool config_complete;
} user_config_t;

/* Application Context */
typedef struct {
    /* Application State */
    app_state_t current_state;
    uint32_t uptime_seconds;
    uint32_t error_count;

    /* Configuration */
    user_config_t user_config;

    /* Network Status */
    network_status_t network_status;

    /* MQTT Status */
    mqtt_status_t mqtt_status;
    ql_mqtt_client_t mqtt_client;

    /* BL0942 Data */
    bl0942_data_t energy_data;
    uint32_t sample_count;

    /* Task Handles */
    ql_task_t main_task;
    ql_task_t network_task;
    ql_task_t mqtt_task;
    ql_task_t bl0942_task;

    /* Synchronization */
    ql_mutex_t data_mutex;
    ql_sem_t config_ready_sem;
    ql_sem_t network_ready_sem;
    ql_sem_t mqtt_ready_sem;

    /* Buffers */
    char json_buffer[JSON_BUFFER_SIZE];
    char uart_rx_buffer[UART_RX_BUFFER_SIZE];
    char uart_tx_buffer[UART_TX_BUFFER_SIZE];

} app_context_t;

/* Function Prototypes */

/**
 * @brief Initialize the application
 * @return 0 on success, negative on error
 */
int app_init(void);

/**
 * @brief Start the application
 * @return 0 on success, negative on error
 */
int app_start(void);

/**
 * @brief Stop the application
 * @return 0 on success, negative on error
 */
int app_stop(void);

/**
 * @brief Get application context
 * @return Pointer to application context
 */
app_context_t* app_get_context(void);

/* Task Entry Points */
void main_task_entry(void *param);
void network_task_entry(void *param);
void mqtt_task_entry(void *param);
void bl0942_task_entry(void *param);

/* Network Functions */
int network_init(void);
int network_register(void);
int network_start_data_call(void);
int network_get_status(network_status_t *status);
const char* network_tech_to_string(network_tech_t tech);

/* MQTT Functions */
int mqtt_init(void);
int mqtt_connect(void);
int mqtt_disconnect(void);
int mqtt_publish_energy_data(const bl0942_data_t *data);
void mqtt_event_callback(ql_mqtt_client_t *client, void *user_data, ql_mqtt_event_info_t *event_info);

/* BL0942 Functions */
int bl0942_init(void);
int bl0942_read_data(bl0942_data_t *data);
int bl0942_read_packet(uint8_t *buffer, uint8_t buffer_size);
int bl0942_parse_packet(const uint8_t *packet, bl0942_data_t *data);
int bl0942_read_register(uint8_t reg_addr, uint32_t *value);
void bl0942_convert_values(bl0942_data_t *data);
uint8_t bl0942_calculate_checksum(const uint8_t *data, uint8_t len, uint8_t cmd);

/* Debug and Utility Functions */
void debug_init(void);
void debug_print(const char *format, ...);
void debug_print_network_status(const network_status_t *status);
void debug_print_mqtt_status(const mqtt_status_t *status);
void debug_print_energy_data(const bl0942_data_t *data);
void debug_print_app_status(void);

/* User Input Functions */
int user_input_init(void);
int user_input_get_credentials(user_config_t *config);
void user_input_task_entry(void *param);

/* JSON Formatting */
int format_energy_json(const bl0942_data_t *data, char *buffer, size_t buffer_size);

/* Utility Functions */
uint32_t get_timestamp(void);
void delay_ms(uint32_t ms);
int validate_config(const user_config_t *config);

/* Error Handling */
void handle_error(const char *function, int error_code);
void reset_application(void);

/* AT Command Helpers */
int send_at_command(const char *cmd, char *response, size_t response_size, uint32_t timeout_ms);
int wait_for_response(const char *expected, uint32_t timeout_ms);

#ifdef __cplusplus
}
#endif

#endif /* MQTT_ENERGY_MONITOR_H */
