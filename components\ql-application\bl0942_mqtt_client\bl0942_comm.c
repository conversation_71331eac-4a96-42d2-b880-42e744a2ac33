/**
 * @file bl0942_comm.c
 * @brief BL0942 Energy Meter Communication
 * @version 1.0
 * @date 2024
 */

#include "mqtt_energy_monitor.h"
#include "ql_api_uart.h"
#include "ql_log.h"
#include <string.h>

#define BL0942_LOG_TAG "BL0942"

/* BL0942 Register Addresses */
#define BL0942_REG_I_WAVE           0x01    // Current waveform
#define BL0942_REG_V_WAVE           0x02    // Voltage waveform
#define BL0942_REG_I_RMS            0x03    // Current RMS
#define BL0942_REG_V_RMS            0x04    // Voltage RMS
#define BL0942_REG_I_FAST_RMS       0x05    // Current fast RMS
#define BL0942_REG_WATT             0x06    // Active power
#define BL0942_REG_CF_CNT           0x07    // Energy pulse counter
#define BL0942_REG_FREQ             0x08    // Line frequency
#define BL0942_REG_STATUS           0x09    // Status register

/* BL0942 Commands */
#define BL0942_CMD_READ             0x58    // Read command
#define BL0942_CMD_WRITE            0xA8    // Write command
#define BL0942_CMD_PACKET_READ      0xAA    // Packet read command
#define BL0942_PACKET_HEADER        0x55    // Packet header

/* UART configuration */
static ql_uart_config_t bl0942_uart_config;
static bool bl0942_initialized = false;
static ql_mutex_t bl0942_uart_mutex;

/**
 * @brief Initialize BL0942 communication
 */
int bl0942_init(void)
{
    if (bl0942_initialized) {
        debug_print("BL0942 already initialized\n");
        return 0;
    }

    debug_print("Initializing BL0942 communication...\n");

    /* Create UART mutex */
    if (ql_rtos_mutex_create(&bl0942_uart_mutex) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to create BL0942 UART mutex\n");
        return -1;
    }

    /* Configure UART */
    bl0942_uart_config.baudrate = BL0942_UART_BAUDRATE;
    bl0942_uart_config.data_bit = BL0942_UART_DATABIT;
    bl0942_uart_config.stop_bit = BL0942_UART_STOPBIT;
    bl0942_uart_config.parity_bit = BL0942_UART_PARITY;
    bl0942_uart_config.flow_ctrl = BL0942_UART_FLOWCTRL;

    /* Open UART port */
    if (ql_uart_open(BL0942_UART_PORT, &bl0942_uart_config) != QL_UART_SUCCESS) {
        debug_print("ERROR: Failed to open BL0942 UART port\n");
        ql_rtos_mutex_delete(bl0942_uart_mutex);
        return -1;
    }

    debug_print("BL0942 UART opened: Port=%d, Baud=4800\n",
                BL0942_UART_PORT);

    /* Wait for UART to stabilize */
    delay_ms(100);

    /* Test communication by reading status register */
    uint32_t status;
    if (bl0942_read_register(BL0942_REG_STATUS, &status) == 0) {
        debug_print("BL0942 communication test successful, status: 0x%06X\n", status);
        bl0942_initialized = true;
    } else {
        debug_print("ERROR: BL0942 communication test failed\n");
        ql_uart_close(BL0942_UART_PORT);
        ql_rtos_mutex_delete(bl0942_uart_mutex);
        return -1;
    }

    debug_print("BL0942 initialized successfully\n");
    return 0;
}

/**
 * @brief BL0942 task entry point
 */
void bl0942_task_entry(void *param)
{
    debug_print("BL0942 task started\n");

    app_context_t *ctx = app_get_context();
    uint32_t last_sample_time = 0;

    while (true) {
        uint32_t current_time = get_timestamp();

        /* Sample energy data periodically */
        if (current_time - last_sample_time >= ENERGY_SAMPLE_INTERVAL_MS) {
            debug_print("Reading BL0942 energy data...\n");

            bl0942_data_t new_data;
            if (bl0942_read_data(&new_data) == 0) {
                /* Update application context with new data */
                ql_rtos_mutex_lock(ctx->data_mutex, QL_WAIT_FOREVER);
                memcpy(&ctx->energy_data, &new_data, sizeof(bl0942_data_t));
                ctx->sample_count++;
                ql_rtos_mutex_unlock(ctx->data_mutex);

                debug_print("Energy data updated (sample #%u)\n", ctx->sample_count);
                debug_print_energy_data(&new_data);
            } else {
                debug_print("ERROR: Failed to read BL0942 data\n");
                handle_error("bl0942_read_data", -1);

                /* Mark data as invalid */
                ql_rtos_mutex_lock(ctx->data_mutex, QL_WAIT_FOREVER);
                ctx->energy_data.valid = false;
                ql_rtos_mutex_unlock(ctx->data_mutex);
            }

            last_sample_time = current_time;
        }

        delay_ms(1000);
    }
}

/**
 * @brief Read energy data from BL0942
 */
int bl0942_read_data(bl0942_data_t *data)
{
    if (!data || !bl0942_initialized) {
        return -1;
    }

    debug_print("Reading BL0942 energy data using packet mode...\n");

    /* Use packet read mode for efficiency */
    uint8_t packet_buffer[23]; // BL0942 packet size

    if (bl0942_read_packet(packet_buffer, sizeof(packet_buffer)) != 0) {
        debug_print("ERROR: Failed to read BL0942 packet\n");
        return -1;
    }

    /* Parse packet data */
    if (bl0942_parse_packet(packet_buffer, data) != 0) {
        debug_print("ERROR: Failed to parse BL0942 packet\n");
        return -1;
    }

    /* Convert raw values to physical units */
    bl0942_convert_values(data);

    data->timestamp = get_timestamp();
    data->valid = true;

    debug_print("BL0942 data read successfully\n");
    return 0;
}

/**
 * @brief Read BL0942 packet
 */
int bl0942_read_packet(uint8_t *buffer, uint8_t buffer_size)
{
    if (!buffer || buffer_size < 23) {
        return -1;
    }

    /* Lock UART access */
    if (ql_rtos_mutex_lock(bl0942_uart_mutex, 1000) != QL_OSI_SUCCESS) {
        debug_print("ERROR: Failed to lock BL0942 UART mutex\n");
        return -1;
    }

    /* Send packet read command */
    uint8_t cmd[2] = {BL0942_CMD_READ, BL0942_CMD_PACKET_READ};

    if (ql_uart_write(BL0942_UART_PORT, cmd, sizeof(cmd)) != QL_UART_SUCCESS) {
        debug_print("ERROR: Failed to send BL0942 packet read command\n");
        ql_rtos_mutex_unlock(bl0942_uart_mutex);
        return -1;
    }

    /* Wait for response */
    delay_ms(60); // BL0942 needs time to prepare packet (longer at 4800 baud)

    /* Read response */
    uint32_t bytes_read = 0;
    uint32_t total_read = 0;
    uint32_t start_time = get_timestamp();

    while (total_read < 23 && (get_timestamp() - start_time) < 1000) {
        if (ql_uart_read(BL0942_UART_PORT, &buffer[total_read], 23 - total_read) > 0) {
            total_read += bytes_read;
        }
        delay_ms(10);
    }

    ql_rtos_mutex_unlock(bl0942_uart_mutex);

    if (total_read != 23) {
        debug_print("ERROR: Incomplete BL0942 packet read (%u/23 bytes)\n", total_read);
        return -1;
    }

    /* Verify packet header */
    if (buffer[0] != BL0942_PACKET_HEADER) {
        debug_print("ERROR: Invalid BL0942 packet header: 0x%02X\n", buffer[0]);
        return -1;
    }

    /* Verify checksum */
    uint8_t calculated_checksum = bl0942_calculate_checksum(&buffer[1], 21,
                                                           BL0942_CMD_READ + BL0942_PACKET_HEADER);
    if (calculated_checksum != buffer[22]) {
        debug_print("ERROR: BL0942 packet checksum mismatch\n");
        return -1;
    }

    debug_print("BL0942 packet read successfully\n");
    return 0;
}

/**
 * @brief Parse BL0942 packet
 */
int bl0942_parse_packet(const uint8_t *packet, bl0942_data_t *data)
{
    if (!packet || !data) {
        return -1;
    }

    /* Extract data from packet (little-endian format) */
    data->current_rms = (uint32_t)packet[1] | ((uint32_t)packet[2] << 8) | ((uint32_t)packet[3] << 16);
    data->voltage_rms = (uint32_t)packet[4] | ((uint32_t)packet[5] << 8) | ((uint32_t)packet[6] << 16);

    uint32_t i_fast_rms = (uint32_t)packet[7] | ((uint32_t)packet[8] << 8) | ((uint32_t)packet[9] << 16);

    uint32_t power_raw = (uint32_t)packet[10] | ((uint32_t)packet[11] << 8) | ((uint32_t)packet[12] << 16);
    data->power = (int32_t)power_raw;

    /* Handle signed power value */
    if (data->power & 0x800000) {
        data->power |= 0xFF000000; // Sign extend
    }

    data->energy_count = (uint32_t)packet[13] | ((uint32_t)packet[14] << 8) | ((uint32_t)packet[15] << 16);
    data->frequency = (uint16_t)packet[16] | ((uint16_t)packet[17] << 8);
    data->status = (uint16_t)packet[19];

    debug_print("Parsed BL0942 packet: I=%u, V=%u, P=%d, E=%u, F=%u\n",
                data->current_rms, data->voltage_rms, data->power,
                data->energy_count, data->frequency);

    return 0;
}

/**
 * @brief Convert raw BL0942 values to physical units
 */
void bl0942_convert_values(bl0942_data_t *data)
{
    if (!data) {
        return;
    }

    const float V_REF = 1.218f; // BL0942 reference voltage

    /* Convert current RMS to mA (assuming 1mΩ shunt and gain=16) */
    data->current_rms = (uint32_t)((float)data->current_rms * V_REF / 305978.0f * 1000.0f);

    /* Convert voltage RMS to mV */
    data->voltage_rms = (uint32_t)((float)data->voltage_rms * V_REF / 73989.0f * 1000.0f);

    /* Convert power to mW */
    data->power = (int32_t)((float)data->power * V_REF * V_REF / 3537.0f * 1000.0f);

    /* Convert frequency (resolution 2us/LSB) */
    if (data->frequency > 0) {
        data->frequency = (uint16_t)(1000000.0f / (float)data->frequency * 100.0f); // Hz * 100
    } else {
        data->frequency = 0;
    }

    debug_print("Converted values: I=%umA, V=%umV, P=%dmW, F=%u.%02uHz\n",
                data->current_rms, data->voltage_rms, data->power,
                data->frequency / 100, data->frequency % 100);
}

/**
 * @brief Read single BL0942 register
 */
int bl0942_read_register(uint8_t reg_addr, uint32_t *value)
{
    if (!value || !bl0942_initialized) {
        return -1;
    }

    /* Lock UART access */
    if (ql_rtos_mutex_lock(bl0942_uart_mutex, 1000) != QL_OSI_SUCCESS) {
        return -1;
    }

    /* Send read command */
    uint8_t cmd[2] = {BL0942_CMD_READ, reg_addr};

    if (ql_uart_write(BL0942_UART_PORT, cmd, sizeof(cmd)) != QL_UART_SUCCESS) {
        ql_rtos_mutex_unlock(bl0942_uart_mutex);
        return -1;
    }

    /* Wait for response */
    delay_ms(10);

    /* Read response (3 data bytes + 1 checksum) */
    uint8_t response[4];
    uint32_t bytes_read = 0;

    if (ql_uart_read(BL0942_UART_PORT, response, sizeof(response)) != sizeof(response)) {
        ql_rtos_mutex_unlock(bl0942_uart_mutex);
        return -1;
    }

    ql_rtos_mutex_unlock(bl0942_uart_mutex);

    /* Verify checksum */
    uint8_t calculated_checksum = bl0942_calculate_checksum(response, 3, BL0942_CMD_READ + reg_addr);
    if (calculated_checksum != response[3]) {
        debug_print("ERROR: BL0942 register read checksum mismatch\n");
        return -1;
    }

    /* Combine data bytes (little-endian) */
    *value = (uint32_t)response[0] | ((uint32_t)response[1] << 8) | ((uint32_t)response[2] << 16);

    return 0;
}

/**
 * @brief Calculate BL0942 checksum
 */
uint8_t bl0942_calculate_checksum(const uint8_t *data, uint8_t len, uint8_t cmd)
{
    uint8_t sum = cmd;

    for (uint8_t i = 0; i < len; i++) {
        sum += data[i];
    }

    return ~sum; // Invert all bits
}
